# 肺结节检测与分类系统 - 多模态融合实现指南

## 1. 系统概述

本指南详细说明了肺结节检测与分类系统中多模态融合部分的实现方法。系统将CT影像特征与临床特征进行融合，实现对肺结节良恶性的精确分类。

## 2. 环境配置

### 2.1 依赖库

使用`uv`管理项目依赖：

```bash
# 安装核心依赖
uv add torch torchvision monai numpy pandas matplotlib
uv add nibabel pydicom SimpleITK scikit-learn

# 可视化工具
uv add seaborn tensorboard
```

### 2.2 项目结构

```
AI-in-Lung-Detection/
├── config/                     # 配置文件
│   ├── classification_config.json
│   ├── config_test.json
│   └── environment.json
├── ct_detection/               # 结节检测模块
│   └── ...
├── ct_classification/          # 结节分类模块
│   ├── classification_models.py
│   └── ...
├── detect_and_classify.py      # 检测与分类集成脚本
├── visualize_noduls.py         # 检测结果可视化
└── visualize_classification.py # 分类结果可视化
```

## 3. 多模态融合实现

### 3.1 特征提取

#### 3.1.1 CT影像特征提取

```python
def extract_nodule_features(model, nodule_patch):
    """
    使用预训练模型提取结节区域的深度特征
    
    Args:
        model: 预训练的特征提取模型
        nodule_patch: 结节区域张量 [B, C, D, H, W]
        
    Returns:
        features: 提取的特征向量
    """
    # 确保模型处于评估模式
    model.eval()
    
    with torch.no_grad():
        # 获取特征（使用中间层特征）
        features = model.extract_features(nodule_patch)
        
        # 应用全局池化
        features = F.adaptive_avg_pool3d(features, (1, 1, 1))
        features = features.view(features.size(0), -1)
        
    return features
```

#### 3.1.2 纹理特征提取

```python
def extract_texture_features(nodule_data):
    """
    提取结节区域的纹理特征
    
    Args:
        nodule_data: 结节区域的numpy数组
        
    Returns:
        texture_features: 纹理特征向量
    """
    # 计算灰度共生矩阵
    glcm_features = []
    
    # 对多个方向和距离计算GLCM
    for distance in [1, 3]:
        for angle in [0, np.pi/4, np.pi/2, 3*np.pi/4]:
            glcm = skimage.feature.graycomatrix(
                nodule_data, 
                distances=[distance], 
                angles=[angle], 
                levels=16, 
                symmetric=True, 
                normed=True
            )
            
            # 提取GLCM统计量
            contrast = skimage.feature.graycoprops(glcm, 'contrast')[0, 0]
            dissimilarity = skimage.feature.graycoprops(glcm, 'dissimilarity')[0, 0]
            homogeneity = skimage.feature.graycoprops(glcm, 'homogeneity')[0, 0]
            energy = skimage.feature.graycoprops(glcm, 'energy')[0, 0]
            correlation = skimage.feature.graycoprops(glcm, 'correlation')[0, 0]
            
            glcm_features.extend([contrast, dissimilarity, homogeneity, energy, correlation])
    
    # 添加直方图特征
    hist_features = np.histogram(nodule_data, bins=10)[0]
    hist_features = hist_features / hist_features.sum()  # 归一化
    
    # 组合所有纹理特征
    texture_features = np.concatenate([glcm_features, hist_features])
    
    return texture_features
```

### 3.2 多模态融合实现

#### 3.2.1 特征向量融合模块

```python
class MultiModalFusion(nn.Module):
    """多模态特征融合模块"""
    
    def __init__(self, img_feature_dim, texture_feature_dim, clinical_feature_dim=0, fusion_dim=512):
        super(MultiModalFusion, self).__init__()
        
        self.has_clinical = clinical_feature_dim > 0
        total_dim = img_feature_dim + texture_feature_dim + clinical_feature_dim
        
        # 特征降维层
        self.img_projection = nn.Linear(img_feature_dim, fusion_dim)
        self.texture_projection = nn.Linear(texture_feature_dim, fusion_dim)
        
        if self.has_clinical:
            self.clinical_projection = nn.Linear(clinical_feature_dim, fusion_dim)
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(fusion_dim, num_heads=8, batch_first=True)
        
        # 融合后处理
        self.fusion_fc = nn.Sequential(
            nn.Linear(fusion_dim, fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(fusion_dim, fusion_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(fusion_dim // 2, 2)  # 二分类：良性/恶性
        )
        
    def forward(self, img_features, texture_features, clinical_features=None):
        # 特征投影
        img_proj = self.img_projection(img_features)
        texture_proj = self.texture_projection(texture_features)
        
        # 准备注意力输入
        if self.has_clinical and clinical_features is not None:
            clinical_proj = self.clinical_projection(clinical_features)
            fusion_input = torch.stack([img_proj, texture_proj, clinical_proj], dim=1)
        else:
            fusion_input = torch.stack([img_proj, texture_proj], dim=1)
        
        # 应用自注意力机制
        attn_output, _ = self.attention(fusion_input, fusion_input, fusion_input)
        
        # 取平均值作为融合特征
        fused_features = torch.mean(attn_output, dim=1)
        
        # 分类层
        output = self.fusion_fc(fused_features)
        
        return output, fused_features
```

#### 3.2.2 完整分类模型

```python
class MultiModalClassifier(nn.Module):
    """结合多模态特征的肺结节分类器"""
    
    def __init__(self, backbone="resnet50", pretrained=True):
        super(MultiModalClassifier, self).__init__()
        
        # 图像特征提取骨干网络
        if backbone == "resnet50":
            self.backbone = generate_model(50, n_input_channels=1)
            self.img_feature_dim = 2048
        elif backbone == "vit":
            self.backbone = timm.create_model(
                'vit_base_patch16_224_in21k', 
                pretrained=pretrained,
                in_chans=1
            )
            self.img_feature_dim = 768
            
        # 纹理特征维度
        self.texture_feature_dim = 50  # GLCM特征 + 直方图特征
        
        # 融合模块
        self.fusion_module = MultiModalFusion(
            img_feature_dim=self.img_feature_dim,
            texture_feature_dim=self.texture_feature_dim
        )
        
    def forward(self, images, texture_features):
        # 图像特征提取
        img_features = self.extract_features(images)
        
        # 特征融合与分类
        output, fused_features = self.fusion_module(img_features, texture_features)
        
        return output, fused_features
        
    def extract_features(self, x):
        """提取图像特征"""
        with torch.no_grad():
            features = self.backbone(x)
            
            # 根据backbone类型处理特征
            if isinstance(features, torch.Tensor):
                features = F.adaptive_avg_pool3d(features, (1, 1, 1))
                features = features.view(features.size(0), -1)
            else:
                features = features.view(features.size(0), -1)
                
        return features
```

### 3.3 模型训练

```python
def train_multimodal_model(model, train_loader, val_loader, device, num_epochs=50):
    """训练多模态融合模型"""
    
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.0001, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=5, verbose=True
    )
    
    best_auc = 0.0
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for batch_idx, batch in enumerate(train_loader):
            images = batch['image'].to(device)
            texture_features = batch['texture_features'].to(device)
            labels = batch['label'].to(device)
            
            optimizer.zero_grad()
            outputs, _ = model(images, texture_features)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            
        # 验证阶段
        model.eval()
        val_loss = 0.0
        all_labels = []
        all_probs = []
        
        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(device)
                texture_features = batch['texture_features'].to(device)
                labels = batch['label'].to(device)
                
                outputs, _ = model(images, texture_features)
                loss = criterion(outputs, labels)
                val_loss += loss.item()
                
                probs = F.softmax(outputs, dim=1)[:, 1]  # 取恶性类别的概率
                
                all_labels.extend(labels.cpu().numpy())
                all_probs.extend(probs.cpu().numpy())
                
        # 计算AUC
        auc = roc_auc_score(all_labels, all_probs)
        
        # 学习率调整
        scheduler.step(auc)
        
        # 保存最佳模型
        if auc > best_auc:
            best_auc = auc
            torch.save(model.state_dict(), "best_multimodal_model.pt")
            
        # 打印训练信息
        print(f"Epoch {epoch+1}/{num_epochs}, Train Loss: {train_loss/len(train_loader):.4f}, "
              f"Val Loss: {val_loss/len(val_loader):.4f}, AUC: {auc:.4f}")
```

## 4. 模型评估与解释

### 4.1 模型评估代码

```python
def evaluate_model(model, test_loader, device):
    """评估模型性能"""
    model.eval()
    all_labels = []
    all_probs = []
    all_features = []
    
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)
            texture_features = batch['texture_features'].to(device)
            labels = batch['label'].to(device)
            
            outputs, fused_features = model(images, texture_features)
            probs = F.softmax(outputs, dim=1)[:, 1]  # 恶性的概率
            
            all_labels.extend(labels.cpu().numpy())
            all_probs.extend(probs.cpu().numpy())
            all_features.extend(fused_features.cpu().numpy())
    
    # 转换为numpy数组
    all_labels = np.array(all_labels)
    all_probs = np.array(all_probs)
    all_features = np.array(all_features)
    
    # 计算评估指标
    auc = roc_auc_score(all_labels, all_probs)
    
    # 以0.5为阈值计算分类指标
    predictions = (all_probs >= 0.5).astype(int)
    accuracy = accuracy_score(all_labels, predictions)
    sensitivity = recall_score(all_labels, predictions)
    precision_val = precision_score(all_labels, predictions)
    f1 = f1_score(all_labels, predictions)
    
    # 打印评估结果
    print(f"测试集评估结果:")
    print(f"AUC: {auc:.4f}")
    print(f"准确率: {accuracy:.4f}")
    print(f"敏感性: {sensitivity:.4f}")
    print(f"精确率: {precision_val:.4f}")
    print(f"F1分数: {f1:.4f}")
    
    # 绘制ROC曲线
    fpr, tpr, _ = roc_curve(all_labels, all_probs)
    plt.figure(figsize=(8, 6))
    plt.plot(fpr, tpr, label=f'AUC = {auc:.4f}')
    plt.plot([0, 1], [0, 1], 'k--')
    plt.xlabel('假阳性率')
    plt.ylabel('真阳性率')
    plt.title('ROC曲线')
    plt.legend()
    plt.savefig('roc_curve.png', dpi=300)
    
    # 返回评估结果和特征
    return {
        'auc': auc,
        'accuracy': accuracy,
        'sensitivity': sensitivity,
        'precision': precision_val,
        'f1': f1,
        'features': all_features,
        'labels': all_labels,
        'probabilities': all_probs
    }
```

## 5. 实施建议

### 5.1 多模态融合最佳实践

1. **数据预处理与标准化**：
   - 确保不同模态的数据经过适当的归一化处理
   - 对图像进行窗位调整以增强结节对比度
   - 对临床特征进行标准化和编码

2. **特征选择**：
   - 使用主成分分析(PCA)或递归特征消除(RFE)减少特征维度
   - 选择与目标变量相关性高的特征

3. **模型融合策略**：
   - 尝试不同的融合策略：早期融合、晚期融合和混合融合
   - 使用注意力机制优化模态间的交互

4. **模型训练技巧**：
   - 实施学习率调度，如余弦退火或ReduceLROnPlateau
   - 使用早停机制防止过拟合
   - 尝试不同的优化器，如Adam、AdamW或SGD

5. **模型解释性**：
   - 使用Grad-CAM等技术可视化模型关注区域
   - 分析特征重要性以了解各模态的贡献

### 5.2 常见问题与解决方案

1. **特征不平衡**：
   - 问题：不同模态的特征维度差异大
   - 解决方案：对每个模态使用单独的投影层进行维度调整

2. **模态缺失**：
   - 问题：某些样本可能缺少特定模态的数据
   - 解决方案：实现能够处理缺失模态的架构，如加权融合或零填充

3. **计算资源限制**：
   - 问题：3D卷积网络需要大量GPU内存
   - 解决方案：使用混合精度训练，或考虑基于2.5D的方法

4. **小样本问题**：
   - 问题：医学数据集通常规模较小
   - 解决方案：使用数据增强、预训练模型和正则化技术

## 6. 参考资源

- PyTorch官方文档: https://pytorch.org/docs/
- MONAI框架: https://monai.io/
- MedicalNet预训练模型: https://github.com/Tencent/MedicalNet
- 肺结节分析相关论文:
  - Shen W, et al. Multi-crop Convolutional Neural Networks for lung nodule malignancy suspiciousness classification. Pattern Recognition, 2019.
  - Hussein S, et al. Risk Stratification of Lung Nodules Using 3D CNN-Based Multi-task Learning. IPMI, 2017. 