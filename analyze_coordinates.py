import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import matplotlib.gridspec as gridspec
from pathlib import Path
import argparse

# 导入相关文件中的函数
try:
    from visualize_nodules_simplified import (
        load_nifti_image,
        load_dicom_series,
        normalize_to_uint8,
        adjust_coordinates_for_visualization,
        create_triplanar_visualization,
        create_summary_visualization,
        visualize_nodules_triplanar
    )
    from generate_annotated_images import load_json_predictions
except ImportError:
    print("警告: 未能导入所有必要的函数，将使用基本实现")
    # 基本加载函数实现
    def load_json_predictions(json_file):
        with open(json_file, 'r') as f:
            return json.load(f)

def print_header(title):
    """打印格式化的标题"""
    print("\n" + "=" * 80)
    print(f"  {title}")
    print("=" * 80)

def analyze_prediction_format(predictions_file):
    """分析预测结果JSON文件的格式"""
    print_header("预测结果文件分析")
    
    try:
        with open(predictions_file, 'r') as f:
            predictions = json.load(f)
        
        print(f"JSON文件: {predictions_file}")
        print(f"顶级键: {list(predictions.keys())}")
        
        # 检查是否包含validation键
        if "validation" in predictions:
            validation = predictions["validation"]
            print(f"验证集预测数量: {len(validation)}")
            
            if len(validation) > 0:
                # 分析第一个预测
                first_pred = validation[0]
                print(f"\n第一个预测结果的键: {list(first_pred.keys())}")
                
                # 分析图像路径
                if "image" in first_pred:
                    print(f"图像路径: {first_pred['image']}")
                
                # 分析边界框格式
                if "box" in first_pred:
                    boxes = first_pred["box"]
                    print(f"边界框数量: {len(boxes)}")
                    if len(boxes) > 0:
                        print(f"第一个边界框: {boxes[0]}")
                        print(f"边界框格式: [x1, y1, z1, x2, y2, z2]")
                        
                        # 计算边界框统计信息
                        box_dims = []
                        for box in boxes:
                            width = box[3] - box[0]
                            height = box[4] - box[1]
                            depth = box[5] - box[2]
                            box_dims.append((width, height, depth))
                        
                        if box_dims:
                            widths, heights, depths = zip(*box_dims)
                            print(f"边界框尺寸统计:")
                            print(f"  宽度: 最小={min(widths):.1f}, 最大={max(widths):.1f}, 平均={sum(widths)/len(widths):.1f}")
                            print(f"  高度: 最小={min(heights):.1f}, 最大={max(heights):.1f}, 平均={sum(heights)/len(heights):.1f}")
                            print(f"  深度: 最小={min(depths):.1f}, 最大={max(depths):.1f}, 平均={sum(depths)/len(depths):.1f}")
                
                # 分析分数格式
                if "score" in first_pred:
                    scores = first_pred["score"]
                    print(f"分数数量: {len(scores)}")
                    if len(scores) > 0:
                        print(f"分数范围: {min(scores):.4f} - {max(scores):.4f}")
                
                # 检查是否包含坐标系信息
                if "affine" in first_pred:
                    print("\n检测到仿射矩阵:")
                    print(np.array(first_pred["affine"]))
        
        # 检查coordinate_system_info
        if "coordinate_system_info" in predictions:
            print("\n坐标系统信息:")
            coord_info = predictions["coordinate_system_info"]
            for key, value in coord_info.items():
                print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"分析预测文件时出错: {e}")

def analyze_image_format(image_path):
    """分析图像格式和维度"""
    print_header("图像数据分析")
    
    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"错误: 图像路径不存在 - {image_path}")
            return
        
        print(f"图像路径: {image_path}")
        
        # 确定是NIfTI还是DICOM
        is_nifti = image_path.lower().endswith(('.nii', '.nii.gz'))
        
        if is_nifti:
            print("检测到NIfTI格式")
            try:
                import nibabel as nib
                img = nib.load(image_path)
                data = img.get_fdata()
                affine = img.affine
                header = img.header
                
                print(f"图像形状: {data.shape}")
                print(f"数据类型: {data.dtype}")
                print(f"值范围: [{np.min(data)}, {np.max(data)}]")
                print(f"仿射矩阵:\n{affine}")
                
                # 分析仿射矩阵
                det = np.linalg.det(affine[:3, :3])
                print(f"仿射矩阵行列式: {det}")
                
                # 尝试确定坐标系
                if det > 0:
                    print("可能的坐标系: RAS (右-前-上)")
                else:
                    print("可能的坐标系: LPS (左-后-上) 或其他")
                
                # 检查轴向顺序
                zooms = header.get_zooms()
                print(f"体素尺寸: {zooms}")
                
                # 创建示例切片
                center = [dim // 2 for dim in data.shape]
                axial_slice = data[:, :, center[2]]
                coronal_slice = data[:, center[1], :]
                sagittal_slice = data[center[0], :, :]
                
                print(f"轴状切片形状: {axial_slice.shape}")
                print(f"冠状切片形状: {coronal_slice.shape}")
                print(f"矢状切片形状: {sagittal_slice.shape}")
                
            except ImportError:
                print("警告: 缺少nibabel库，无法完全分析NIfTI文件")
        else:
            print("假设为DICOM目录")
            try:
                import pydicom
                import SimpleITK as sitk
                
                # 假设输入是DICOM目录
                dicom_dir = os.path.dirname(image_path)
                
                # 列出DICOM文件
                dicom_files = []
                for root, _, files in os.walk(dicom_dir):
                    for file in files:
                        if file.endswith('.dcm'):
                            dicom_files.append(os.path.join(root, file))
                
                if not dicom_files:
                    print(f"警告: 未找到DICOM文件在 {dicom_dir}")
                    return
                
                print(f"DICOM文件数量: {len(dicom_files)}")
                
                # 读取第一个DICOM文件获取基本信息
                first_dcm = pydicom.dcmread(dicom_files[0])
                print(f"DICOM图像尺寸: {first_dcm.Rows} x {first_dcm.Columns}")
                if hasattr(first_dcm, 'PatientPosition'):
                    print(f"患者位置: {first_dcm.PatientPosition}")
                if hasattr(first_dcm, 'ImageOrientationPatient'):
                    print(f"图像方向: {first_dcm.ImageOrientationPatient}")
                
                # 尝试读取整个系列
                reader = sitk.ImageSeriesReader()
                dicom_names = reader.GetGDCMSeriesFileNames(dicom_dir)
                reader.SetFileNames(dicom_names)
                image = reader.Execute()
                array = sitk.GetArrayFromImage(image)
                
                print(f"完整DICOM系列形状: {array.shape}")
                print(f"注意: DICOM数组形状通常为 (z, y, x)")
                print(f"数据类型: {array.dtype}")
                print(f"值范围: [{np.min(array)}, {np.max(array)}]")
                
                # 获取方向和间距信息
                spacing = image.GetSpacing()
                direction = image.GetDirection()
                origin = image.GetOrigin()
                
                print(f"体素间距: {spacing}")
                print(f"方向矩阵: {direction}")
                print(f"原点: {origin}")
                
            except ImportError:
                print("警告: 缺少pydicom或SimpleITK库，无法完全分析DICOM文件")
    
    except Exception as e:
        print(f"分析图像时出错: {e}")

def analyze_coordinate_conversion():
    """分析坐标转换代码"""
    print_header("坐标转换分析")
    
    # 创建测试数据
    image_shape = (512, 512, 128)
    test_boxes = [
        [100, 100, 50, 150, 150, 70],  # 一个居中的边界框
        [10, 10, 10, 30, 30, 20],      # 一个靠近原点的边界框
        [450, 450, 100, 490, 490, 120] # 一个靠近边缘的边界框
    ]
    
    print("测试图像形状:", image_shape)
    print("\n测试边界框:")
    
    for i, box in enumerate(test_boxes):
        print(f"\n边界框 #{i+1}: {box}")
        
        # 计算边界框特性
        width = box[3] - box[0]
        height = box[4] - box[1]
        depth = box[5] - box[2]
        center_x = (box[0] + box[3]) / 2
        center_y = (box[1] + box[4]) / 2
        center_z = (box[2] + box[5]) / 2
        
        print(f"尺寸: 宽={width}, 高={height}, 深={depth}")
        print(f"中心点: ({center_x}, {center_y}, {center_z})")
        
        # 测试不同的z_offset值
        for z_offset in [0, 10, -10]:
            try:
                # 使用可视化代码中的函数
                adjusted_box = adjust_coordinates_for_visualization(box, image_shape, z_offset)
                
                # 计算调整后的特性
                adj_width = adjusted_box[3] - adjusted_box[0]
                adj_height = adjusted_box[4] - adjusted_box[1]
                adj_depth = adjusted_box[5] - adjusted_box[2]
                adj_center_x = (adjusted_box[0] + adjusted_box[3]) / 2
                adj_center_y = (adjusted_box[1] + adjusted_box[4]) / 2
                adj_center_z = (adjusted_box[2] + adjusted_box[5]) / 2
                
                print(f"\nz_offset={z_offset}时的调整结果:")
                print(f"调整后边界框: {adjusted_box}")
                print(f"调整后尺寸: 宽={adj_width}, 高={adj_height}, 深={adj_depth}")
                print(f"调整后中心点: ({adj_center_x}, {adj_center_y}, {adj_center_z})")
                
                # 计算变化
                print(f"中心点变化: x差异={adj_center_x-center_x}, y差异={adj_center_y-center_y}, z差异={adj_center_z-center_z}")
                
            except Exception as e:
                print(f"测试adjust_coordinates_for_visualization时出错: {e}")
    
    # 模拟坐标系转换
    print("\n模拟坐标系转换 (LPS <-> RAS):")
    test_point = [100, 200, 50]
    print(f"原始点 (RAS): {test_point}")
    
    # RAS到LPS转换
    lps_point = [-test_point[0], -test_point[1], test_point[2]]
    print(f"转换到LPS: {lps_point}")
    
    # LPS回到RAS
    ras_point = [-lps_point[0], -lps_point[1], lps_point[2]]
    print(f"转回RAS: {ras_point}")

def analyze_drawing_logic():
    """分析边界框绘制逻辑"""
    print_header("边界框绘制逻辑分析")
    
    # 创建示例数据
    test_shape = (100, 100, 50)
    test_data = np.zeros(test_shape)
    
    # 在中心区域创建一个明显的结构
    x_center, y_center, z_center = [dim // 2 for dim in test_shape]
    radius = min(test_shape) // 4
    
    for x in range(test_shape[0]):
        for y in range(test_shape[1]):
            for z in range(test_shape[2]):
                dist = np.sqrt((x - x_center)**2 + (y - y_center)**2 + (z - z_center)**2)
                if dist < radius:
                    test_data[x, y, z] = 100
    
    # 添加一些噪声
    test_data += np.random.normal(0, 10, test_data.shape)
    test_data = np.clip(test_data, 0, 255)
    
    # 创建测试边界框
    test_box = [
        x_center - radius, 
        y_center - radius, 
        z_center - radius // 2,
        x_center + radius, 
        y_center + radius, 
        z_center + radius // 2
    ]
    
    print(f"测试数据形状: {test_shape}")
    print(f"对象中心: ({x_center}, {y_center}, {z_center})")
    print(f"测试边界框: {test_box}")
    
    # 提取三个平面的切片
    axial_slice = test_data[:, :, z_center].T
    coronal_slice = test_data[:, y_center, :].T
    sagittal_slice = test_data[x_center, :, :].T
    
    print(f"轴状切片形状: {axial_slice.shape}")
    print(f"冠状切片形状: {coronal_slice.shape}")
    print(f"矢状切片形状: {sagittal_slice.shape}")
    
    # 分析matplotlib绘图坐标系
    print("\nMatplotlib绘图坐标系分析:")
    print("imshow默认原点: 左上角 (0,0)")
    print("x坐标从左到右增加")
    print("y坐标从上到下增加")
    
    # 分析每个平面的坐标映射
    print("\n轴状视图 (Axial View) 坐标映射:")
    print("显示的是x-y平面上的切片")
    print("由于转置操作(.T)，图像的行列已交换:")
    print("  原始数据坐标 (x, y, z) -> 图像坐标 (y, x)")
    
    print("\n冠状视图 (Coronal View) 坐标映射:")
    print("显示的是x-z平面上的切片")
    print("由于转置操作(.T)，图像的行列已交换:")
    print("  原始数据坐标 (x, y, z) -> 图像坐标 (z, x)")
    
    print("\n矢状视图 (Sagittal View) 坐标映射:")
    print("显示的是y-z平面上的切片")
    print("由于转置操作(.T)，图像的行列已交换:")
    print("  原始数据坐标 (x, y, z) -> 图像坐标 (z, y)")
    
    # 在轴状视图上绘制边界框的分析
    print("\n在轴状视图上绘制边界框的正确方式:")
    print(f"边界框原始坐标: [x1={test_box[0]}, y1={test_box[1]}, z1={test_box[2]}, x2={test_box[3]}, y2={test_box[4]}, z2={test_box[5]}]")
    
    # 分析当前create_triplanar_visualization中的绘制方法
    print("\n当前绘制方法分析:")
    print("- 轴状视图: 使用(y1, x1)作为左上角坐标，宽度为(y2-y1)，高度为(x2-x1)")
    print("- 冠状视图: 使用(z1, x1)作为左上角坐标，宽度为(z2-z1)，高度为(x2-x1)")
    print("- 矢状视图: 使用(z1, y1)作为左上角坐标，宽度为(z2-z1)，高度为(y2-y1)")
    
    # 创建可视化示例
    try:
        fig = plt.figure(figsize=(15, 5))
        gs = gridspec.GridSpec(1, 3, width_ratios=[1, 1, 1])
        
        # 轴状视图
        ax1 = plt.subplot(gs[0])
        ax1.imshow(axial_slice, cmap='gray')
        
        x_min_axial = max(0, test_box[1])  # y1
        y_min_axial = max(0, test_box[0])  # x1
        width_axial = min(test_box[4] - test_box[1], axial_slice.shape[1] - x_min_axial)  # y2-y1
        height_axial = min(test_box[3] - test_box[0], axial_slice.shape[0] - y_min_axial)  # x2-x1
        
        rect_axial = Rectangle(
            (x_min_axial, y_min_axial), width_axial, height_axial,
            linewidth=2, edgecolor='r', facecolor='none'
        )
        ax1.add_patch(rect_axial)
        ax1.set_title('Axial View')
        ax1.axis('off')
        
        # 冠状视图
        ax2 = plt.subplot(gs[1])
        ax2.imshow(coronal_slice, cmap='gray')
        
        x_min_coronal = max(0, test_box[2])  # z1
        y_min_coronal = max(0, test_box[0])  # x1
        width_coronal = min(test_box[5] - test_box[2], coronal_slice.shape[1] - x_min_coronal)  # z2-z1
        height_coronal = min(test_box[3] - test_box[0], coronal_slice.shape[0] - y_min_coronal)  # x2-x1
        
        rect_coronal = Rectangle(
            (x_min_coronal, y_min_coronal), width_coronal, height_coronal,
            linewidth=2, edgecolor='r', facecolor='none'
        )
        ax2.add_patch(rect_coronal)
        ax2.set_title('Coronal View')
        ax2.axis('off')
        
        # 矢状视图
        ax3 = plt.subplot(gs[2])
        ax3.imshow(sagittal_slice, cmap='gray')
        
        x_min_sagittal = max(0, test_box[2])  # z1
        y_min_sagittal = max(0, test_box[1])  # y1
        width_sagittal = min(test_box[5] - test_box[2], sagittal_slice.shape[1] - x_min_sagittal)  # z2-z1
        height_sagittal = min(test_box[4] - test_box[1], sagittal_slice.shape[0] - y_min_sagittal)  # y2-y1
        
        rect_sagittal = Rectangle(
            (x_min_sagittal, y_min_sagittal), width_sagittal, height_sagittal,
            linewidth=2, edgecolor='r', facecolor='none'
        )
        ax3.add_patch(rect_sagittal)
        ax3.set_title('Sagittal View')
        ax3.axis('off')
        
        plt.suptitle('边界框绘制测试', fontsize=16)
        
        # 保存图像
        os.makedirs("analysis_output", exist_ok=True)
        plt.tight_layout()
        plt.savefig("analysis_output/drawing_test.png", dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"\n绘制测试图像已保存至: analysis_output/drawing_test.png")
        
    except Exception as e:
        print(f"创建测试图像时出错: {e}")

def analyze_coordinate_mapping(image_data, box):
    """
    分析坐标映射关系，针对不同视图的映射进行可视化说明
    
    参数：
        image_data: 3D图像数据
        box: 边界框坐标 [x1, y1, z1, x2, y2, z2]
    """
    print("\n===== 坐标映射关系分析 =====")
    print(f"原始边界框: {box}")
    
    # 计算边界框中心
    center_x = int((box[0] + box[3]) / 2)
    center_y = int((box[1] + box[4]) / 2)
    center_z = int((box[2] + box[5]) / 2)
    print(f"边界框中心点: ({center_x}, {center_y}, {center_z})")
    
    # 原始图像形状
    print(f"图像形状: {image_data.shape}")
    
    # 分析不同视图中的坐标映射
    print("\n1. 原始3D空间中的坐标系:")
    print("   X轴: 从左到右增加")
    print("   Y轴: 从前到后增加")
    print("   Z轴: 从下到上增加")
    
    print("\n2. 轴状视图 (Axial View - 固定z):")
    print("   - 显示的是x-y平面")
    print("   - 转置后(.T)，图像的行列已交换:")
    print(f"   - 转置前切片形状: {image_data[:, :, center_z].shape}")
    print(f"   - 转置后切片形状: {image_data[:, :, center_z].T.shape}")
    print("   - 坐标映射: 3D空间(x,y,z) -> 2D图像(y,x)")
    print("   - 边界框左上角: (y1, x1) = ({}, {})".format(box[1], box[0]))
    print("   - 边界框右下角: (y2, x2) = ({}, {})".format(box[4], box[3]))
    
    print("\n3. 冠状视图 (Coronal View - 固定y):")
    print("   - 显示的是x-z平面")
    print("   - 转置后(.T)，图像的行列已交换:")
    print(f"   - 转置前切片形状: {image_data[:, center_y, :].shape}")
    print(f"   - 转置后切片形状: {image_data[:, center_y, :].T.shape}")
    print("   - 坐标映射: 3D空间(x,y,z) -> 2D图像(z,x)")
    print("   - 边界框左上角: (z1, x1) = ({}, {})".format(box[2], box[0]))
    print("   - 边界框右下角: (z2, x2) = ({}, {})".format(box[5], box[3]))
    
    print("\n4. 矢状视图 (Sagittal View - 固定x):")
    print("   - 显示的是y-z平面")
    print("   - 转置后(.T)，图像的行列已交换:")
    print(f"   - 转置前切片形状: {image_data[center_x, :, :].shape}")
    print(f"   - 转置后切片形状: {image_data[center_x, :, :].T.shape}")
    print("   - 坐标映射: 3D空间(x,y,z) -> 2D图像(z,y)")
    print("   - 边界框左上角: (z1, y1) = ({}, {})".format(box[2], box[1]))
    print("   - 边界框右下角: (z2, y2) = ({}, {})".format(box[5], box[4]))
    
    print("\n5. Matplotlib绘图坐标系:")
    print("   - 原点: 左上角(0,0)")
    print("   - X轴: 从左到右增加")
    print("   - Y轴: 从上到下增加")
    
    # RAS与LPS坐标系转换分析
    print("\n6. 坐标系转换 (LPS <-> RAS):")
    print("   LPS (左-后-上): X轴指向左，Y轴指向后，Z轴指向上")
    print("   RAS (右-前-上): X轴指向右，Y轴指向前，Z轴指向上")
    
    # 假设box是LPS坐标系下的坐标
    lps_box = box
    print(f"   LPS坐标下的边界框: {lps_box}")
    
    # 转换到RAS
    ras_box = [-lps_box[0], -lps_box[1], lps_box[2], -lps_box[3], -lps_box[4], lps_box[5]]
    print(f"   RAS坐标下的边界框: {ras_box}")
    
    # 计算中心点
    lps_center = [(lps_box[0] + lps_box[3])/2, (lps_box[1] + lps_box[4])/2, (lps_box[2] + lps_box[5])/2]
    ras_center = [(ras_box[0] + ras_box[3])/2, (ras_box[1] + ras_box[4])/2, (ras_box[2] + ras_box[5])/2]
    print(f"   LPS坐标下的中心点: ({lps_center[0]}, {lps_center[1]}, {lps_center[2]})")
    print(f"   RAS坐标下的中心点: ({ras_center[0]}, {ras_center[1]}, {ras_center[2]})")
    
    # 如果是反向转换（box是RAS坐标系下的坐标）
    print("\n   如果假设原始边界框是RAS坐标:")
    ras_box_alt = box
    lps_box_alt = [-ras_box_alt[0], -ras_box_alt[1], ras_box_alt[2], -ras_box_alt[3], -ras_box_alt[4], ras_box_alt[5]]
    print(f"   RAS坐标下的边界框: {ras_box_alt}")
    print(f"   转换到LPS的边界框: {lps_box_alt}")
    
    # 图像坐标系与患者坐标系对齐问题
    print("\n7. 图像坐标系与患者坐标系对齐:")
    print("   当图像被加载到可视化软件中时，软件需要确定如何将图像体素坐标映射到显示空间")
    print("   不同软件的默认行为可能不同，导致坐标转换和显示的差异")
    print("   关键问题是确保可视化代码使用与图像数据相同的约定")
    
    print("\n8. 建议:")
    print("   - 检查预测边界框的坐标系（RAS还是LPS）")
    print("   - 确认可视化代码中的坐标转换逻辑与数据加载方式一致")
    print("   - 考虑在visualize_nodules_simplified.py中实现显式的坐标系转换")
    print("   - 对比generate_annotated_images.py中的坐标处理逻辑")
    print("   - 测试不同的坐标翻转组合，找到正确的显示方式")
    
    print("="*50)

def analyze_visualization_script(predictions_file):
    """分析visualize_nodules_simplified.py脚本的运行情况"""
    print_header("可视化脚本分析")
    
    try:
        # 尝试导入visualize_nodules_simplified.py中的函数
        from visualize_nodules_simplified import (
            adjust_coordinates_for_visualization,
            create_triplanar_visualization,
            create_summary_visualization,
            normalize_to_uint8,
            visualize_nodules_triplanar
        )
        
        # 加载预测结果
        print(f"加载预测文件: {predictions_file}")
        predictions = load_json_predictions(predictions_file)
        validation_predictions = predictions.get("validation", [])
        
        if not validation_predictions:
            print("预测结果为空，请检查JSON文件")
            return
        
        # 分析第一个预测结果
        pred = validation_predictions[0]
        image_path = pred["image"]
        boxes = pred["box"]
        scores = pred["score"]
        
        print(f"分析第一个预测结果的可视化过程:")
        print(f"图像路径: {image_path}")
        print(f"边界框数量: {len(boxes)}")
        print(f"检测到的结节数: {len([s for s in scores if s >= 0.5])}")
        
        # 创建测试输出目录
        test_output_dir = "analysis_output/viz_test"
        os.makedirs(test_output_dir, exist_ok=True)
        print(f"测试输出目录: {test_output_dir}")
        
        # 分析边界框坐标调整
        print("\n分析边界框坐标调整:")
        for i, (box, score) in enumerate(zip(boxes[:3], scores[:3])):  # 只分析前3个边界框
            print(f"\n边界框 #{i+1}: {box}, 置信度: {score:.4f}")
            
            # 模拟调整坐标的步骤
            if image_path.lower().endswith(('.nii', '.nii.gz')):
                try:
                    import nibabel as nib
                    img = nib.load(image_path)
                    image_data = img.get_fdata()
                    print(f"加载的NIfTI图像形状: {image_data.shape}")
                    
                    # 分析坐标映射关系（仅对第一个边界框）
                    if i == 0:
                        analyze_coordinate_mapping(image_data, box)
                    
                    # 调整坐标
                    adjusted_box = adjust_coordinates_for_visualization(box, image_data.shape)
                    print(f"调整后的边界框: {adjusted_box}")
                    
                    # 计算边界框中心
                    x_center = int((adjusted_box[0] + adjusted_box[3]) / 2)
                    y_center = int((adjusted_box[1] + adjusted_box[4]) / 2)
                    z_center = int((adjusted_box[2] + adjusted_box[5]) / 2)
                    print(f"边界框中心点: ({x_center}, {y_center}, {z_center})")
                    
                    # 计算边界框尺寸
                    width = adjusted_box[3] - adjusted_box[0]
                    height = adjusted_box[4] - adjusted_box[1]
                    depth = adjusted_box[5] - adjusted_box[2]
                    print(f"边界框尺寸: 宽度={width}, 高度={height}, 深度={depth}")
                    
                    # 分析各个视图中的边界框绘制逻辑
                    print("\n绘图逻辑分析:")
                    
                    # 轴状视图 (Axial - 固定z)
                    print("轴状视图 (Axial):")
                    axial_slice = image_data[:, :, z_center].T
                    print(f"切片形状: {axial_slice.shape}")
                    x_min = max(0, adjusted_box[1])  # y1作为x
                    y_min = max(0, adjusted_box[0])  # x1作为y
                    x_max = min(axial_slice.shape[1]-1, adjusted_box[4])  # y2
                    y_max = min(axial_slice.shape[0]-1, adjusted_box[3])  # x2
                    print(f"绘制的矩形: 左上角=({x_min}, {y_min}), 右下角=({x_max}, {y_max})")
                    print(f"矩形尺寸: 宽度={x_max-x_min}, 高度={y_max-y_min}")
                    
                    # 冠状视图 (Coronal - 固定y)
                    print("\n冠状视图 (Coronal):")
                    coronal_slice = image_data[:, y_center, :].T
                    print(f"切片形状: {coronal_slice.shape}")
                    x_min = max(0, adjusted_box[2])  # z1作为x
                    y_min = max(0, adjusted_box[0])  # x1作为y
                    x_max = min(coronal_slice.shape[1]-1, adjusted_box[5])  # z2
                    y_max = min(coronal_slice.shape[0]-1, adjusted_box[3])  # x2
                    print(f"绘制的矩形: 左上角=({x_min}, {y_min}), 右下角=({x_max}, {y_max})")
                    print(f"矩形尺寸: 宽度={x_max-x_min}, 高度={y_max-y_min}")
                    
                    # 矢状视图 (Sagittal - 固定x)
                    print("\n矢状视图 (Sagittal):")
                    sagittal_slice = image_data[x_center, :, :].T
                    print(f"切片形状: {sagittal_slice.shape}")
                    x_min = max(0, adjusted_box[2])  # z1作为x
                    y_min = max(0, adjusted_box[1])  # y1作为y
                    x_max = min(sagittal_slice.shape[1]-1, adjusted_box[5])  # z2
                    y_max = min(sagittal_slice.shape[0]-1, adjusted_box[4])  # y2
                    print(f"绘制的矩形: 左上角=({x_min}, {y_min}), 右下角=({x_max}, {y_max})")
                    print(f"矩形尺寸: 宽度={x_max-x_min}, 高度={y_max-y_min}")
                    
                    # 尝试创建一个实际的可视化示例
                    if i == 0:  # 只为第一个边界框创建可视化
                        test_file = os.path.join(test_output_dir, f"test_nodule_{i+1}.png")
                        print(f"\n创建测试可视化: {test_file}")
                        create_triplanar_visualization(
                            image_data,
                            adjusted_box,
                            score,
                            test_file,
                            i,
                            lower_bound=-1000,
                            upper_bound=100
                        )
                        print(f"测试可视化已保存: {test_file}")
                
                except ImportError:
                    print("警告: 无法加载NIfTI文件，请确保安装了nibabel库")
                except Exception as e:
                    print(f"分析NIfTI文件时出错: {e}")
            else:
                print(f"未支持的图像格式: {image_path}")
        
        # 检查坐标系信息
        if "coordinate_system_info" in predictions:
            print("\n坐标系统信息:")
            coord_info = predictions["coordinate_system_info"]
            for key, value in coord_info.items():
                print(f"  {key}: {value}")
                
            # 分析LPS到RAS的转换
            if coord_info.get("affine_lps_to_ras"):
                print("\nLPS到RAS转换分析:")
                print("根据坐标系统信息，预测坐标使用了从LPS到RAS的转换")
                print("LPS (左-后-上): X轴指向左，Y轴指向后，Z轴指向上")
                print("RAS (右-前-上): X轴指向右，Y轴指向前，Z轴指向上")
                print("转换逻辑: X_RAS = -X_LPS, Y_RAS = -Y_LPS, Z_RAS = Z_LPS")
                
                # 模拟转换的例子
                lps_example = [100, 200, 50]
                ras_example = [-lps_example[0], -lps_example[1], lps_example[2]]
                print(f"例子: LPS坐标 {lps_example} -> RAS坐标 {ras_example}")
                
                # 与可视化坐标系的关系
                print("\n与可视化的关系:")
                print("图像显示坐标系通常使用左上角为原点的系统")
                print("轴状视图 (Axial): X轴从左到右，Y轴从上到下")
                print("冠状视图 (Coronal): X轴从左到右，Y轴从上到下")
                print("矢状视图 (Sagittal): X轴从左到右，Y轴从上到下")
                print("这需要额外的转换以在正确的位置显示边界框")
        
        # 模拟visualize_nodules_triplanar的调用
        print("\n模拟运行visualize_nodules_triplanar:")
        print(f"参数: 图像路径={image_path}, 边界框数量={len(boxes)}")
        print(f"      阈值=0.5, 窗位=[-1000, 100]")
        print("注意: 这只是一个模拟，不会实际运行完整的可视化流程")
        
        # 模拟阈值过滤
        filtered_boxes = []
        filtered_scores = []
        for i, (box, score) in enumerate(zip(boxes, scores)):
            if score >= 0.5:
                filtered_boxes.append(box)
                filtered_scores.append(score)
        
        print(f"过滤后的边界框数量: {len(filtered_boxes)}/{len(boxes)}")
        
        # 测试不同的坐标转换方法对可视化效果的影响
        print("\n测试不同的坐标转换方法:")
        if len(filtered_boxes) > 0 and image_path.lower().endswith(('.nii', '.nii.gz')):
            try:
                # 选择第一个边界框进行测试
                test_box = filtered_boxes[0]
                test_score = filtered_scores[0]
                
                # 1. 原始方法 - 不进行翻转
                test_file1 = os.path.join(test_output_dir, "method1_original.png")
                print(f"\n方法1 - 原始方法（不翻转）:")
                print(f"边界框: {test_box}")
                create_triplanar_visualization(
                    image_data,
                    test_box,
                    test_score,
                    test_file1,
                    0,
                    lower_bound=-1000,
                    upper_bound=100
                )
                print(f"测试可视化已保存: {test_file1}")
                
                # 2. 翻转X坐标
                flipped_x_box = [
                    image_data.shape[0] - test_box[3],  # x1 = dim_x - x2
                    test_box[1],                       # y1
                    test_box[2],                       # z1
                    image_data.shape[0] - test_box[0], # x2 = dim_x - x1
                    test_box[4],                       # y2
                    test_box[5]                        # z2
                ]
                test_file2 = os.path.join(test_output_dir, "method2_flip_x.png")
                print(f"\n方法2 - 翻转X坐标:")
                print(f"边界框: {flipped_x_box}")
                create_triplanar_visualization(
                    image_data,
                    flipped_x_box,
                    test_score,
                    test_file2,
                    0,
                    lower_bound=-1000,
                    upper_bound=100
                )
                print(f"测试可视化已保存: {test_file2}")
                
                # 3. 翻转Y坐标
                flipped_y_box = [
                    test_box[0],                       # x1
                    image_data.shape[1] - test_box[4], # y1 = dim_y - y2
                    test_box[2],                       # z1
                    test_box[3],                       # x2
                    image_data.shape[1] - test_box[1], # y2 = dim_y - y1
                    test_box[5]                        # z2
                ]
                test_file3 = os.path.join(test_output_dir, "method3_flip_y.png")
                print(f"\n方法3 - 翻转Y坐标:")
                print(f"边界框: {flipped_y_box}")
                create_triplanar_visualization(
                    image_data,
                    flipped_y_box,
                    test_score,
                    test_file3,
                    0,
                    lower_bound=-1000,
                    upper_bound=100
                )
                print(f"测试可视化已保存: {test_file3}")
                
                # 4. 翻转X和Y坐标
                flipped_xy_box = [
                    image_data.shape[0] - test_box[3], # x1 = dim_x - x2
                    image_data.shape[1] - test_box[4], # y1 = dim_y - y2
                    test_box[2],                       # z1
                    image_data.shape[0] - test_box[0], # x2 = dim_x - x1
                    image_data.shape[1] - test_box[1], # y2 = dim_y - y1
                    test_box[5]                        # z2
                ]
                test_file4 = os.path.join(test_output_dir, "method4_flip_xy.png")
                print(f"\n方法4 - 翻转X和Y坐标:")
                print(f"边界框: {flipped_xy_box}")
                create_triplanar_visualization(
                    image_data,
                    flipped_xy_box,
                    test_score,
                    test_file4,
                    0,
                    lower_bound=-1000,
                    upper_bound=100
                )
                print(f"测试可视化已保存: {test_file4}")
                
                print("\n所有测试方法的可视化结果已保存到 " + test_output_dir)
                print("请比较这些结果，确定哪种方法的边界框与实际肺结节位置最匹配")
                
            except Exception as e:
                print(f"测试不同坐标转换方法时出错: {e}")
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保可以导入visualize_nodules_simplified.py中的函数")
    except Exception as e:
        print(f"分析可视化脚本时出错: {e}")

def compare_visualization_methods(predictions_file):
    """
    比较visualize_nodules_simplified.py和generate_annotated_images.py的坐标处理逻辑
    
    参数：
        predictions_file: 预测结果JSON文件路径
    """
    print_header("可视化方法比较分析")
    
    try:
        # 从visualize_nodules_simplified.py导入函数
        from visualize_nodules_simplified import adjust_coordinates_for_visualization
        
        # 从generate_annotated_images.py导入函数
        from generate_annotated_images import (
            load_json_predictions, 
            add_bounding_boxes_to_image,
            process_image_with_options
        )
        
        # 创建输出目录
        comparison_dir = "analysis_output/method_comparison"
        os.makedirs(comparison_dir, exist_ok=True)
        
        # 加载预测结果
        print(f"加载预测文件: {predictions_file}")
        predictions = load_json_predictions(predictions_file)
        validation_predictions = predictions.get("validation", [])
        
        if not validation_predictions:
            print("预测结果为空，请检查JSON文件")
            return
        
        # 分析第一个预测结果
        pred = validation_predictions[0]
        image_path = pred["image"]
        boxes = pred["box"]
        scores = pred["score"]
        
        print(f"分析第一个预测结果: {image_path}")
        print(f"边界框数量: {len(boxes)}")
        
        # 查找高置信度的结节
        high_conf_boxes = []
        high_conf_scores = []
        for i, (box, score) in enumerate(zip(boxes, scores)):
            if score >= 0.8:  # 只分析高置信度的结节
                high_conf_boxes.append(box)
                high_conf_scores.append(score)
                print(f"高置信度结节 #{len(high_conf_boxes)}: 边界框={box}, 置信度={score:.4f}")
        
        if not high_conf_boxes:
            print("未找到高置信度结节，使用所有结节中置信度最高的前3个")
            # 按置信度排序
            sorted_indices = sorted(range(len(scores)), key=lambda i: scores[i], reverse=True)
            for i in sorted_indices[:3]:
                high_conf_boxes.append(boxes[i])
                high_conf_scores.append(scores[i])
                print(f"选定结节 #{len(high_conf_boxes)}: 边界框={boxes[i]}, 置信度={scores[i]:.4f}")
        
        # 载入图像
        print(f"载入图像: {image_path}")
        if image_path.lower().endswith(('.nii', '.nii.gz')):
            try:
                import nibabel as nib
                img = nib.load(image_path)
                image_data = img.get_fdata()
                affine = img.affine
                print(f"NIfTI图像形状: {image_data.shape}")
                print(f"NIfTI仿射矩阵:\n{affine}")
                
                # 分析仿射矩阵
                det = np.linalg.det(affine[:3, :3])
                print(f"仿射矩阵行列式: {det}")
                if det > 0:
                    print("坐标系可能为: RAS (右-前-上)")
                else:
                    print("坐标系可能为: LPS (左-后-上) 或其他")
                
                # 分析visualize_nodules_simplified.py中的坐标处理
                print("\n1. visualize_nodules_simplified.py中的坐标处理:")
                for i, (box, score) in enumerate(zip(high_conf_boxes, high_conf_scores)):
                    print(f"\n分析结节 #{i+1}:")
                    print(f"原始边界框: {box}")
                    
                    # 使用adjust_coordinates_for_visualization
                    adjusted_box = adjust_coordinates_for_visualization(box, image_data.shape)
                    print(f"调整后边界框: {adjusted_box}")
                    
                    # 计算边界框中心和尺寸
                    center_x = (adjusted_box[0] + adjusted_box[3]) / 2
                    center_y = (adjusted_box[1] + adjusted_box[4]) / 2
                    center_z = (adjusted_box[2] + adjusted_box[5]) / 2
                    width = adjusted_box[3] - adjusted_box[0]
                    height = adjusted_box[4] - adjusted_box[1]
                    depth = adjusted_box[5] - adjusted_box[2]
                    
                    print(f"中心点: ({center_x}, {center_y}, {center_z})")
                    print(f"尺寸: 宽度={width}, 高度={height}, 深度={depth}")
                
                # 分析generate_annotated_images.py中的坐标处理
                print("\n2. generate_annotated_images.py中的坐标处理:")
                
                # 模拟process_image_with_options函数的关键部分
                try:
                    # 查找add_bounding_boxes_to_image函数的定义
                    import inspect
                    add_box_source = inspect.getsource(add_bounding_boxes_to_image)
                    print("\nadd_bounding_boxes_to_image函数源码片段:")
                    print("\n".join(add_box_source.split("\n")[:20]) + "\n...")  # 只显示前20行
                    
                    # 查找process_image_with_options函数的定义
                    process_img_source = inspect.getsource(process_image_with_options)
                    print("\nprocess_image_with_options函数源码片段:")
                    print("\n".join(process_img_source.split("\n")[:20]) + "\n...")  # 只显示前20行
                    
                    # 分析关键逻辑差异
                    print("\n关键逻辑差异分析:")
                    print("1. generate_annotated_images.py 中使用了仿射矩阵进行坐标转换")
                    print("2. visualize_nodules_simplified.py 中只进行了简单的坐标范围检查")
                    print("3. generate_annotated_images.py 处理了LPS和RAS坐标系之间的转换")
                    print("4. 两者在边界框绘制逻辑上也有差异，特别是坐标映射方式")
                    
                except Exception as e:
                    print(f"分析函数定义时出错: {e}")
                
                # 建议修改
                print("\n建议修改:")
                print("1. 将visualize_nodules_simplified.py中的adjust_coordinates_for_visualization函数修改为:")
                print("   - 明确处理LPS到RAS的坐标转换")
                print("   - 考虑图像仿射矩阵的影响")
                print("   - 确保坐标翻转逻辑与generate_annotated_images.py一致")
                print("2. 调整边界框绘制逻辑，确保在各个视图中正确显示")
                print("3. 添加更多调试信息，帮助追踪坐标转换过程")
                
            except ImportError as e:
                print(f"导入错误: {e}")
                print("请确保安装了nibabel库")
            except Exception as e:
                print(f"分析NIfTI文件时出错: {e}")
        else:
            print(f"不支持的图像格式: {image_path}")
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保可以导入所需的函数")
    except Exception as e:
        print(f"比较可视化方法时出错: {e}")

def run_coordinate_transform_tests(predictions_file, image_path=None):
    """
    运行各种坐标系变换测试，生成不同的可视化结果
    
    参数：
        predictions_file: 预测结果JSON文件路径
        image_path: 可选的图像文件路径，如果为None则使用预测结果中的路径
    """
    print_header("坐标系变换测试")
    
    try:
        # 导入必要的函数
        from visualize_nodules_simplified import (
            create_triplanar_visualization,
            normalize_to_uint8
        )
        
        # 创建测试输出目录
        test_output_dir = "analysis_output/coordinate_tests"
        os.makedirs(test_output_dir, exist_ok=True)
        print(f"测试输出目录: {test_output_dir}")
        
        # 加载预测结果
        print(f"加载预测文件: {predictions_file}")
        predictions = load_json_predictions(predictions_file)
        validation_predictions = predictions.get("validation", [])
        
        if not validation_predictions:
            print("预测结果为空，请检查JSON文件")
            return
        
        # 获取图像路径
        pred = validation_predictions[0]
        if image_path is None:
            image_path = pred["image"]
        
        boxes = pred["box"]
        scores = pred["score"]
        
        print(f"使用图像: {image_path}")
        print(f"边界框数量: {len(boxes)}")
        
        # 选择高置信度的边界框
        test_boxes = []
        test_scores = []
        for i, (box, score) in enumerate(zip(boxes, scores)):
            if score >= 0.8 and len(test_boxes) < 3:  # 最多选择3个高置信度的边界框
                test_boxes.append(box)
                test_scores.append(score)
                print(f"选择边界框 #{i+1}: {box}, 置信度: {score:.4f}")
        
        if not test_boxes and len(boxes) > 0:
            # 如果没有高置信度的边界框，使用置信度最高的那个
            max_score_idx = max(range(len(scores)), key=lambda i: scores[i])
            test_boxes.append(boxes[max_score_idx])
            test_scores.append(scores[max_score_idx])
            print(f"使用置信度最高的边界框: {boxes[max_score_idx]}, 置信度: {scores[max_score_idx]:.4f}")
        
        if not test_boxes:
            print("没有可用的边界框进行测试")
            return
        
        # 加载图像数据
        if image_path.lower().endswith(('.nii', '.nii.gz')):
            try:
                import nibabel as nib
                img = nib.load(image_path)
                image_data = img.get_fdata()
                affine = img.affine
                print(f"NIfTI图像形状: {image_data.shape}")
                print(f"仿射矩阵:\n{affine}")
                
                # 仿射矩阵分析
                det = np.linalg.det(affine[:3, :3])
                print(f"仿射矩阵行列式: {det}")
                if det > 0:
                    print("坐标系可能为: RAS (右-前-上)")
                else:
                    print("坐标系可能为: LPS (左-后-上) 或其他")
                
                # 定义不同的坐标变换方法
                transform_methods = [
                    {
                        "name": "original",
                        "description": "原始坐标，不进行变换",
                        "transform": lambda box, shape: box
                    },
                    {
                        "name": "flip_x",
                        "description": "翻转X坐标",
                        "transform": lambda box, shape: [
                            shape[0] - box[3],  # x1 = dim_x - x2
                            box[1],             # y1
                            box[2],             # z1
                            shape[0] - box[0],  # x2 = dim_x - x1
                            box[4],             # y2
                            box[5]              # z2
                        ]
                    },
                    {
                        "name": "flip_y",
                        "description": "翻转Y坐标",
                        "transform": lambda box, shape: [
                            box[0],             # x1
                            shape[1] - box[4],  # y1 = dim_y - y2
                            box[2],             # z1
                            box[3],             # x2
                            shape[1] - box[1],  # y2 = dim_y - y1
                            box[5]              # z2
                        ]
                    },
                    {
                        "name": "flip_xy",
                        "description": "翻转X和Y坐标",
                        "transform": lambda box, shape: [
                            shape[0] - box[3],  # x1 = dim_x - x2
                            shape[1] - box[4],  # y1 = dim_y - y2
                            box[2],             # z1
                            shape[0] - box[0],  # x2 = dim_x - x1
                            shape[1] - box[1],  # y2 = dim_y - y1
                            box[5]              # z2
                        ]
                    },
                    {
                        "name": "lps_to_ras",
                        "description": "LPS到RAS转换 (x=-x, y=-y, z=z)",
                        "transform": lambda box, shape: [
                            -box[3],  # x1 = -x2
                            -box[4],  # y1 = -y2
                            box[2],   # z1
                            -box[0],  # x2 = -x1
                            -box[1],  # y2 = -y1
                            box[5]    # z2
                        ]
                    },
                    {
                        "name": "ras_to_lps",
                        "description": "RAS到LPS转换 (x=-x, y=-y, z=z)",
                        "transform": lambda box, shape: [
                            -box[3],  # x1 = -x2
                            -box[4],  # y1 = -y2
                            box[2],   # z1
                            -box[0],  # x2 = -x1
                            -box[1],  # y2 = -y1
                            box[5]    # z2
                        ]
                    },
                    {
                        "name": "flip_x_offset",
                        "description": "翻转X坐标并添加偏移",
                        "transform": lambda box, shape: [
                            shape[0] - box[3] - 10,  # x1 = dim_x - x2 - 10
                            box[1],                  # y1
                            box[2],                  # z1
                            shape[0] - box[0] - 10,  # x2 = dim_x - x1 - 10
                            box[4],                  # y2
                            box[5]                   # z2
                        ]
                    },
                    {
                        "name": "flip_y_offset",
                        "description": "翻转Y坐标并添加偏移",
                        "transform": lambda box, shape: [
                            box[0],                  # x1
                            shape[1] - box[4] - 10,  # y1 = dim_y - y2 - 10
                            box[2],                  # z1
                            box[3],                  # x2
                            shape[1] - box[1] - 10,  # y2 = dim_y - y1 - 10
                            box[5]                   # z2
                        ]
                    }
                ]
                
                # 测试每种变换方法
                test_box = test_boxes[0]  # 使用第一个边界框进行测试
                test_score = test_scores[0]
                
                summary_file = os.path.join(test_output_dir, "transform_methods_summary.txt")
                with open(summary_file, "w", encoding="utf-8") as f:
                    f.write("坐标变换方法测试摘要\n")
                    f.write(f"图像路径: {image_path}\n")
                    f.write(f"图像形状: {image_data.shape}\n")
                    f.write(f"原始边界框: {test_box}\n")
                    f.write(f"置信度: {test_score:.4f}\n\n")
                    
                    f.write("测试结果:\n")
                    for method in transform_methods:
                        f.write(f"\n方法: {method['name']}\n")
                        f.write(f"描述: {method['description']}\n")
                        
                        transformed_box = method["transform"](test_box, image_data.shape)
                        f.write(f"变换后边界框: {transformed_box}\n")
                        
                        # 计算中心点
                        center_x = (transformed_box[0] + transformed_box[3]) / 2
                        center_y = (transformed_box[1] + transformed_box[4]) / 2
                        center_z = (transformed_box[2] + transformed_box[5]) / 2
                        
                        # 边界框尺寸
                        width = transformed_box[3] - transformed_box[0]
                        height = transformed_box[4] - transformed_box[1]
                        depth = transformed_box[5] - transformed_box[2]
                        
                        f.write(f"中心点: ({center_x}, {center_y}, {center_z})\n")
                        f.write(f"尺寸: 宽度={width}, 高度={height}, 深度={depth}\n")
                
                print(f"变换方法摘要已保存至: {summary_file}")
                
                # 为每种方法生成可视化结果
                print("\n生成可视化结果:")
                for method in transform_methods:
                    try:
                        transformed_box = method["transform"](test_box, image_data.shape)
                        
                        # 确保坐标在有效范围内
                        transformed_box = [
                            max(0, min(transformed_box[0], image_data.shape[0]-1)),  # x1
                            max(0, min(transformed_box[1], image_data.shape[1]-1)),  # y1
                            max(0, min(transformed_box[2], image_data.shape[2]-1)),  # z1
                            max(0, min(transformed_box[3], image_data.shape[0]-1)),  # x2
                            max(0, min(transformed_box[4], image_data.shape[1]-1)),  # y2
                            max(0, min(transformed_box[5], image_data.shape[2]-1))   # z2
                        ]
                        
                        # 确保x1 < x2, y1 < y2, z1 < z2
                        if transformed_box[0] > transformed_box[3]:
                            transformed_box[0], transformed_box[3] = transformed_box[3], transformed_box[0]
                        if transformed_box[1] > transformed_box[4]:
                            transformed_box[1], transformed_box[4] = transformed_box[4], transformed_box[1]
                        if transformed_box[2] > transformed_box[5]:
                            transformed_box[2], transformed_box[5] = transformed_box[5], transformed_box[2]
                        
                        output_file = os.path.join(test_output_dir, f"method_{method['name']}.png")
                        print(f"方法 {method['name']}: {method['description']}")
                        print(f"变换后边界框: {transformed_box}")
                        
                        create_triplanar_visualization(
                            image_data,
                            transformed_box,
                            test_score,
                            output_file,
                            0,
                            lower_bound=-1000,
                            upper_bound=100
                        )
                        print(f"可视化结果已保存至: {output_file}\n")
                    except Exception as e:
                        print(f"方法 {method['name']} 测试失败: {e}\n")
                
                print(f"所有测试可视化已保存至: {test_output_dir}")
                print("请比较这些结果，确定哪种坐标变换方法最准确地显示肺结节位置")
                
            except ImportError as e:
                print(f"导入错误: {e}")
                print("请确保安装了nibabel库")
            except Exception as e:
                print(f"分析NIfTI文件时出错: {e}")
        else:
            print(f"不支持的图像格式: {image_path}")
    
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保可以导入所需的函数")
    except Exception as e:
        print(f"运行坐标变换测试时出错: {e}")

def run_enhanced_coordinate_tests(predictions_file, image_path=None):
    """
    运行增强版坐标系变换测试，特别关注method_flip_xy变体和边界框对齐
    
    参数：
        predictions_file: 预测结果JSON文件路径
        image_path: 可选的图像文件路径，如果为None则使用预测结果中的路径
    """
    print_header("增强版坐标系变换测试")
    
    try:
        # 导入必要的函数
        from visualize_nodules_simplified import (
            create_triplanar_visualization,
            normalize_to_uint8
        )
        
        # 创建测试输出目录
        test_output_dir = "analysis_output/enhanced_coordinate_tests"
        os.makedirs(test_output_dir, exist_ok=True)
        print(f"测试输出目录: {test_output_dir}")
        
        # 加载预测结果
        print(f"加载预测文件: {predictions_file}")
        predictions = load_json_predictions(predictions_file)
        validation_predictions = predictions.get("validation", [])
        
        if not validation_predictions:
            print("预测结果为空，请检查JSON文件")
            return
        
        # 获取图像路径
        pred = validation_predictions[0]
        if image_path is None:
            image_path = pred["image"]
        
        boxes = pred["box"]
        scores = pred["score"]
        
        print(f"使用图像: {image_path}")
        print(f"边界框数量: {len(boxes)}")
        
        # 选择高置信度的边界框
        test_boxes = []
        test_scores = []
        for i, (box, score) in enumerate(zip(boxes, scores)):
            if score >= 0.8 and len(test_boxes) < 3:  # 最多选择3个高置信度的边界框
                test_boxes.append(box)
                test_scores.append(score)
                print(f"选择边界框 #{i+1}: {box}, 置信度: {score:.4f}")
        
        if not test_boxes and len(boxes) > 0:
            # 如果没有高置信度的边界框，使用置信度最高的那个
            max_score_idx = max(range(len(scores)), key=lambda i: scores[i])
            test_boxes.append(boxes[max_score_idx])
            test_scores.append(scores[max_score_idx])
            print(f"使用置信度最高的边界框: {boxes[max_score_idx]}, 置信度: {scores[max_score_idx]:.4f}")
        
        if not test_boxes:
            print("没有可用的边界框进行测试")
            return
        
        # 加载图像数据
        if image_path.lower().endswith(('.nii', '.nii.gz')):
            try:
                import nibabel as nib
                img = nib.load(image_path)
                image_data = img.get_fdata()
                affine = img.affine
                print(f"NIfTI图像形状: {image_data.shape}")
                print(f"仿射矩阵:\n{affine}")
                
                # 仿射矩阵分析
                det = np.linalg.det(affine[:3, :3])
                print(f"仿射矩阵行列式: {det}")
                if det > 0:
                    print("坐标系可能为: RAS (右-前-上)")
                else:
                    print("坐标系可能为: LPS (左-后-上) 或其他")
                
                # 定义不同的坐标变换方法
                transform_methods = [
                    {
                        "name": "flip_xy",
                        "description": "标准的翻转X和Y坐标",
                        "transform": lambda box, shape: [
                            shape[0] - box[3],  # x1 = dim_x - x2
                            shape[1] - box[4],  # y1 = dim_y - y2
                            box[2],             # z1
                            shape[0] - box[0],  # x2 = dim_x - x1
                            shape[1] - box[1],  # y2 = dim_y - y1
                            box[5]              # z2
                        ]
                    },
                    {
                        "name": "flip_xy_with_rect1",
                        "description": "翻转X和Y坐标 + 矩形绘制调整1",
                        "transform": lambda box, shape: [
                            shape[0] - box[3],  # x1 = dim_x - x2
                            shape[1] - box[4],  # y1 = dim_y - y2
                            box[2],             # z1
                            shape[0] - box[0],  # x2 = dim_x - x1
                            shape[1] - box[1],  # y2 = dim_y - y1
                            box[5]              # z2
                        ],
                        "rect_transform": True
                    },
                    {
                        "name": "flip_xy_offset",
                        "description": "翻转X和Y坐标 + 微小偏移",
                        "transform": lambda box, shape: [
                            shape[0] - box[3] + 5,  # x1 = dim_x - x2 + 5
                            shape[1] - box[4] + 5,  # y1 = dim_y - y2 + 5
                            box[2],                 # z1
                            shape[0] - box[0] + 5,  # x2 = dim_x - x1 + 5
                            shape[1] - box[1] + 5,  # y2 = dim_y - y1 + 5
                            box[5]                  # z2
                        ]
                    },
                    {
                        "name": "lps_to_ras_plus_flip",
                        "description": "LPS到RAS转换 + 翻转XY",
                        "transform": lambda box, shape: [
                            shape[0] - (-box[3]),  # x1 = dim_x - (-x2_lps)
                            shape[1] - (-box[4]),  # y1 = dim_y - (-y2_lps)
                            box[2],                # z1 = z1_lps
                            shape[0] - (-box[0]),  # x2 = dim_x - (-x1_lps)
                            shape[1] - (-box[1]),  # y2 = dim_y - (-y1_lps)
                            box[5]                 # z2 = z2_lps
                        ]
                    },
                    {
                        "name": "custom_method1",
                        "description": "自定义方法1 - 翻转XY + 调整矩形绘制偏移",
                        "transform": lambda box, shape: [
                            shape[0] - box[3],  # x1 = dim_x - x2
                            shape[1] - box[4],  # y1 = dim_y - y2
                            box[2],             # z1
                            shape[0] - box[0],  # x2 = dim_x - x1
                            shape[1] - box[1],  # y2 = dim_y - y1
                            box[5]              # z2
                        ],
                        "rect_adjustment": {"x_offset": -10, "y_offset": -10}
                    }
                ]
                
                # 创建一个修改版的create_triplanar_visualization函数，用于测试不同的矩形绘制方法
                def create_adjusted_triplanar_visualization(image_data, box, score, output_file, nodule_idx, 
                                                           method_info, lower_bound=None, upper_bound=None):
                    """
                    创建单个结节的三平面可视化，并根据method_info应用自定义的矩形绘制调整
                    """
                    # 获取原始实现
                    from visualize_nodules_simplified import create_triplanar_visualization as original_viz
                    
                    # 如果不需要自定义矩形绘制，直接使用原始实现
                    if not method_info.get("rect_transform") and not method_info.get("rect_adjustment"):
                        return original_viz(image_data, box, score, output_file, nodule_idx, lower_bound, upper_bound)
                    
                    print(f"\n创建结节的三平面可视化图像 (使用自定义矩形绘制):")
                    print(f"  - 边界框坐标: {box}")
                    print(f"  - 检测置信度: {score:.4f}")
                    
                    # 计算边界框中心
                    x_center = int((box[0] + box[3]) / 2)
                    y_center = int((box[1] + box[4]) / 2)
                    z_center = int((box[2] + box[5]) / 2)
                    print(f"  - 边界框中心点: ({x_center}, {y_center}, {z_center})")
                    
                    # 提取三个平面的切片
                    axial_slice = image_data[:, :, z_center].T
                    coronal_slice = image_data[:, y_center, :].T
                    sagittal_slice = image_data[x_center, :, :].T
                    
                    # 窗位调整和归一化
                    if lower_bound is not None and upper_bound is not None:
                        axial_slice = np.clip(axial_slice, lower_bound, upper_bound)
                        coronal_slice = np.clip(coronal_slice, lower_bound, upper_bound)
                        sagittal_slice = np.clip(sagittal_slice, lower_bound, upper_bound)
                    
                    # 将图像归一化为0-255范围的uint8类型
                    axial_slice_norm = normalize_to_uint8(axial_slice)
                    coronal_slice_norm = normalize_to_uint8(coronal_slice)
                    sagittal_slice_norm = normalize_to_uint8(sagittal_slice)
                    
                    # 创建图像
                    fig = plt.figure(figsize=(15, 5))
                    gs = gridspec.GridSpec(1, 3, width_ratios=[1, 1, 1])
                    
                    # 获取偏移量（如果有）
                    rect_adjustment = method_info.get("rect_adjustment", {})
                    x_offset = rect_adjustment.get("x_offset", 0)
                    y_offset = rect_adjustment.get("y_offset", 0)
                    
                    # ===== 轴状视图（Axial View - z固定）=====
                    ax1 = plt.subplot(gs[0])
                    ax1.imshow(axial_slice_norm, cmap='gray')
                    
                    img_height, img_width = axial_slice_norm.shape
                    
                    if method_info.get("rect_transform"):
                        # 使用不同的矩形绘制逻辑
                        # 这里我们尝试交换x和y坐标的映射
                        x_min = max(0, box[0]) + x_offset  # 使用x1作为x坐标
                        y_min = max(0, box[1]) + y_offset  # 使用y1作为y坐标
                        x_max = min(img_width-1, box[3]) + x_offset  # 使用x2
                        y_max = min(img_height-1, box[4]) + y_offset  # 使用y2
                    else:
                        # 使用原始的矩形绘制逻辑，但添加偏移
                        x_min = max(0, box[1]) + x_offset  # 使用y1作为x坐标
                        y_min = max(0, box[0]) + y_offset  # 使用x1作为y坐标
                        x_max = min(img_width-1, box[4]) + x_offset  # 使用y2
                        y_max = min(img_height-1, box[3]) + y_offset  # 使用x2
                    
                    rect_width = x_max - x_min
                    rect_height = y_max - y_min
                    
                    rect_axial = Rectangle(
                        (x_min, y_min), rect_width, rect_height,
                        linewidth=2, edgecolor='r', facecolor='none'
                    )
                    ax1.add_patch(rect_axial)
                    ax1.set_title(f'Axial View (z={z_center})')
                    ax1.axis('off')
                    
                    # ===== 冠状视图（Coronal View - y固定）=====
                    ax2 = plt.subplot(gs[1])
                    ax2.imshow(coronal_slice_norm, cmap='gray')
                    
                    img_height, img_width = coronal_slice_norm.shape
                    
                    if method_info.get("rect_transform"):
                        # 使用不同的矩形绘制逻辑
                        x_min = max(0, box[2]) + x_offset  # 使用z1作为x坐标
                        y_min = max(0, box[0]) + y_offset  # 使用x1作为y坐标
                        x_max = min(img_width-1, box[5]) + x_offset  # 使用z2
                        y_max = min(img_height-1, box[3]) + y_offset  # 使用x2
                    else:
                        # 使用原始的矩形绘制逻辑，但添加偏移
                        x_min = max(0, box[2]) + x_offset  # 使用z1作为x坐标
                        y_min = max(0, box[0]) + y_offset  # 使用x1作为y坐标
                        x_max = min(img_width-1, box[5]) + x_offset  # 使用z2
                        y_max = min(img_height-1, box[3]) + y_offset  # 使用x2
                    
                    rect_width = x_max - x_min
                    rect_height = y_max - y_min
                    
                    rect_coronal = Rectangle(
                        (x_min, y_min), rect_width, rect_height,
                        linewidth=2, edgecolor='r', facecolor='none'
                    )
                    ax2.add_patch(rect_coronal)
                    ax2.set_title(f'Coronal View (y={y_center})')
                    ax2.axis('off')
                    
                    # ===== 矢状视图（Sagittal View - x固定）=====
                    ax3 = plt.subplot(gs[2])
                    ax3.imshow(sagittal_slice_norm, cmap='gray')
                    
                    img_height, img_width = sagittal_slice_norm.shape
                    
                    if method_info.get("rect_transform"):
                        # 使用不同的矩形绘制逻辑
                        x_min = max(0, box[2]) + x_offset  # 使用z1作为x坐标
                        y_min = max(0, box[1]) + y_offset  # 使用y1作为y坐标
                        x_max = min(img_width-1, box[5]) + x_offset  # 使用z2
                        y_max = min(img_height-1, box[4]) + y_offset  # 使用y2
                    else:
                        # 使用原始的矩形绘制逻辑，但添加偏移
                        x_min = max(0, box[2]) + x_offset  # 使用z1作为x坐标
                        y_min = max(0, box[1]) + y_offset  # 使用y1作为y坐标
                        x_max = min(img_width-1, box[5]) + x_offset  # 使用z2
                        y_max = min(img_height-1, box[4]) + y_offset  # 使用y2
                    
                    rect_width = x_max - x_min
                    rect_height = y_max - y_min
                    
                    rect_sagittal = Rectangle(
                        (x_min, y_min), rect_width, rect_height,
                        linewidth=2, edgecolor='r', facecolor='none'
                    )
                    ax3.add_patch(rect_sagittal)
                    ax3.set_title(f'Sagittal View (x={x_center})')
                    ax3.axis('off')
                    
                    # 添加置信度信息
                    plt.suptitle(f'Nodule #{nodule_idx+1} - Score: {score:.4f}', fontsize=16)
                    
                    # 保存图像
                    plt.tight_layout()
                    plt.savefig(output_file, dpi=150, bbox_inches='tight')
                    plt.close()
                    
                    print(f"  - 三平面可视化图像已保存至: {output_file}")
                    return True
                
                # 测试每种变换方法
                test_box = test_boxes[0]  # 使用第一个边界框进行测试
                test_score = test_scores[0]
                
                summary_file = os.path.join(test_output_dir, "enhanced_transform_methods_summary.txt")
                with open(summary_file, "w", encoding="utf-8") as f:
                    f.write("增强版坐标变换方法测试摘要\n")
                    f.write(f"图像路径: {image_path}\n")
                    f.write(f"图像形状: {image_data.shape}\n")
                    f.write(f"原始边界框: {test_box}\n")
                    f.write(f"置信度: {test_score:.4f}\n\n")
                    
                    f.write("测试结果:\n")
                    for method in transform_methods:
                        f.write(f"\n方法: {method['name']}\n")
                        f.write(f"描述: {method['description']}\n")
                        
                        transformed_box = method["transform"](test_box, image_data.shape)
                        f.write(f"变换后边界框: {transformed_box}\n")
                        
                        # 计算中心点
                        center_x = (transformed_box[0] + transformed_box[3]) / 2
                        center_y = (transformed_box[1] + transformed_box[4]) / 2
                        center_z = (transformed_box[2] + transformed_box[5]) / 2
                        
                        # 边界框尺寸
                        width = transformed_box[3] - transformed_box[0]
                        height = transformed_box[4] - transformed_box[1]
                        depth = transformed_box[5] - transformed_box[2]
                        
                        f.write(f"中心点: ({center_x}, {center_y}, {center_z})\n")
                        f.write(f"尺寸: 宽度={width}, 高度={height}, 深度={depth}\n")
                        
                        if method.get("rect_transform"):
                            f.write("使用自定义矩形绘制逻辑\n")
                        if method.get("rect_adjustment"):
                            f.write(f"矩形偏移: x={method['rect_adjustment'].get('x_offset', 0)}, y={method['rect_adjustment'].get('y_offset', 0)}\n")
                
                print(f"变换方法摘要已保存至: {summary_file}")
                
                # 为每种方法生成可视化结果
                print("\n生成增强版可视化结果:")
                for method in transform_methods:
                    try:
                        transformed_box = method["transform"](test_box, image_data.shape)
                        
                        # 确保坐标在有效范围内
                        transformed_box = [
                            max(0, min(transformed_box[0], image_data.shape[0]-1)),  # x1
                            max(0, min(transformed_box[1], image_data.shape[1]-1)),  # y1
                            max(0, min(transformed_box[2], image_data.shape[2]-1)),  # z1
                            max(0, min(transformed_box[3], image_data.shape[0]-1)),  # x2
                            max(0, min(transformed_box[4], image_data.shape[1]-1)),  # y2
                            max(0, min(transformed_box[5], image_data.shape[2]-1))   # z2
                        ]
                        
                        # 确保x1 < x2, y1 < y2, z1 < z2
                        if transformed_box[0] > transformed_box[3]:
                            transformed_box[0], transformed_box[3] = transformed_box[3], transformed_box[0]
                        if transformed_box[1] > transformed_box[4]:
                            transformed_box[1], transformed_box[4] = transformed_box[4], transformed_box[1]
                        if transformed_box[2] > transformed_box[5]:
                            transformed_box[2], transformed_box[5] = transformed_box[5], transformed_box[2]
                        
                        output_file = os.path.join(test_output_dir, f"enhanced_{method['name']}.png")
                        print(f"方法 {method['name']}: {method['description']}")
                        print(f"变换后边界框: {transformed_box}")
                        
                        # 使用自定义的可视化函数
                        create_adjusted_triplanar_visualization(
                            image_data,
                            transformed_box,
                            test_score,
                            output_file,
                            0,
                            method,
                            lower_bound=-1000,
                            upper_bound=100
                        )
                        print(f"可视化结果已保存至: {output_file}\n")
                    except Exception as e:
                        print(f"方法 {method['name']} 测试失败: {e}\n")
                
                print(f"所有增强版测试可视化已保存至: {test_output_dir}")
                print("请比较这些结果，确定哪种坐标变换方法和矩形绘制调整最准确地显示肺结节位置")
                
            except ImportError as e:
                print(f"导入错误: {e}")
                print("请确保安装了nibabel库")
            except Exception as e:
                print(f"分析NIfTI文件时出错: {e}")
        else:
            print(f"不支持的图像格式: {image_path}")
    
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保可以导入所需的函数")
    except Exception as e:
        print(f"运行增强版坐标变换测试时出错: {e}")

def modify_drawing_logic_for_flip_xy(image_data, transformed_box, output_file, score, nodule_idx, lower_bound=-1000, upper_bound=100):
    """
    为method_flip_xy方法创建修正边界框绘制逻辑的三平面可视化
    
    参数:
        image_data: 3D图像数据
        transformed_box: 已经经过flip_xy变换的边界框坐标
        output_file: 输出文件路径
        score: 检测置信度
        nodule_idx: 结节索引
        lower_bound: 窗位下限
        upper_bound: 窗位上限
    """
    print(f"\n为method_flip_xy创建修正绘制逻辑的三平面可视化:")
    print(f"  - 变换后边界框: {transformed_box}")
    print(f"  - 检测置信度: {score:.4f}")
    
    # 计算边界框中心
    x_center = int((transformed_box[0] + transformed_box[3]) / 2)
    y_center = int((transformed_box[1] + transformed_box[4]) / 2)
    z_center = int((transformed_box[2] + transformed_box[5]) / 2)
    print(f"  - 边界框中心点: ({x_center}, {y_center}, {z_center})")
    
    # 提取三个平面的切片
    axial_slice = image_data[:, :, z_center].T
    coronal_slice = image_data[:, y_center, :].T
    sagittal_slice = image_data[x_center, :, :].T
    
    # 窗位调整和归一化
    if lower_bound is not None and upper_bound is not None:
        axial_slice = np.clip(axial_slice, lower_bound, upper_bound)
        coronal_slice = np.clip(coronal_slice, lower_bound, upper_bound)
        sagittal_slice = np.clip(sagittal_slice, lower_bound, upper_bound)
    
    # 将图像归一化为0-255范围的uint8类型
    axial_slice_norm = normalize_to_uint8(axial_slice)
    coronal_slice_norm = normalize_to_uint8(coronal_slice)
    sagittal_slice_norm = normalize_to_uint8(sagittal_slice)
    
    # 创建图像
    fig = plt.figure(figsize=(15, 5))
    gs = gridspec.GridSpec(1, 3, width_ratios=[1, 1, 1])
    
    # ===== 轴状视图（Axial View - z固定）=====
    ax1 = plt.subplot(gs[0])
    ax1.imshow(axial_slice_norm, cmap='gray')
    
    # 获取图像尺寸
    img_height, img_width = axial_slice_norm.shape
    
    # 轴状视图上的边界框 - 修正后的绘制逻辑
    # 在轴状视图上，我们直接使用翻转后的x和y坐标
    x_min = max(0, transformed_box[0])  # 使用翻转后的x1
    y_min = max(0, transformed_box[1])  # 使用翻转后的y1
    x_max = min(img_width-1, transformed_box[3])  # 使用翻转后的x2
    y_max = min(img_height-1, transformed_box[4])  # 使用翻转后的y2
    
    rect_width = x_max - x_min
    rect_height = y_max - y_min
    
    rect_axial = Rectangle(
        (x_min, y_min), rect_width, rect_height,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax1.add_patch(rect_axial)
    ax1.set_title(f'Axial View (z={z_center})')
    ax1.axis('off')
    
    # ===== 冠状视图（Coronal View - y固定）=====
    ax2 = plt.subplot(gs[1])
    ax2.imshow(coronal_slice_norm, cmap='gray')
    
    # 冠状视图上的边界框 - 修正后的绘制逻辑
    img_height, img_width = coronal_slice_norm.shape
    
    # 在冠状视图中，我们在图像上显示的是x-z平面，但转置后是z-x平面
    # 因此，我们需要使用z坐标作为水平轴(x)，x坐标作为垂直轴(y)
    x_min_coronal = max(0, transformed_box[2])                # z1作为x坐标
    y_min_coronal = max(0, transformed_box[0])                # 翻转后的x1作为y坐标
    x_max_coronal = min(img_width-1, transformed_box[5])      # z2作为x_max
    y_max_coronal = min(img_height-1, transformed_box[3])     # 翻转后的x2作为y_max
    
    # 关键修正点：可能需要调整z坐标的映射方向，特别是当图像使用不同坐标系时
    # 如果z轴朝上，可能需要翻转z坐标: shape[2] - z1 和 shape[2] - z2
    # 测试两种方式，看哪种能正确定位结节
    
    # 方法1：直接使用z坐标
    rect_width_coronal = x_max_coronal - x_min_coronal
    rect_height_coronal = y_max_coronal - y_min_coronal
    
    rect_coronal = Rectangle(
        (x_min_coronal, y_min_coronal), rect_width_coronal, rect_height_coronal,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax2.add_patch(rect_coronal)
    
    # 方法2：翻转z坐标（如果方法1不起作用，可以取消下面注释尝试）
    # z_flip_min = img_width-1 - transformed_box[5]  # 翻转z2
    # z_flip_max = img_width-1 - transformed_box[2]  # 翻转z1
    # rect_width_flip = z_flip_max - z_flip_min
    # rect_coronal_flip = Rectangle(
    #     (z_flip_min, y_min_coronal), rect_width_flip, rect_height_coronal,
    #     linewidth=2, edgecolor='g', facecolor='none'
    # )
    # ax2.add_patch(rect_coronal_flip)
    
    ax2.set_title(f'Coronal View (y={y_center})')
    ax2.axis('off')
    
    # ===== 矢状视图（Sagittal View - x固定）=====
    ax3 = plt.subplot(gs[2])
    ax3.imshow(sagittal_slice_norm, cmap='gray')
    
    # 矢状视图上的边界框 - 修正后的绘制逻辑
    img_height, img_width = sagittal_slice_norm.shape
    
    # 在矢状视图中，我们在图像上显示的是y-z平面，但转置后是z-y平面
    # 因此，我们需要使用z坐标作为水平轴(x)，y坐标作为垂直轴(y)
    x_min_sagittal = max(0, transformed_box[2])                # z1作为x坐标
    y_min_sagittal = max(0, transformed_box[1])                # 翻转后的y1作为y坐标
    x_max_sagittal = min(img_width-1, transformed_box[5])      # z2作为x_max
    y_max_sagittal = min(img_height-1, transformed_box[4])     # 翻转后的y2作为y_max
    
    # 同样，可能需要调整z坐标的映射方向
    # 方法1：直接使用z坐标
    rect_width_sagittal = x_max_sagittal - x_min_sagittal
    rect_height_sagittal = y_max_sagittal - y_min_sagittal
    
    rect_sagittal = Rectangle(
        (x_min_sagittal, y_min_sagittal), rect_width_sagittal, rect_height_sagittal,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax3.add_patch(rect_sagittal)
    
    # 方法2：翻转z坐标（如果方法1不起作用，可以取消下面注释尝试）
    # z_flip_min = img_width-1 - transformed_box[5]  # 翻转z2
    # z_flip_max = img_width-1 - transformed_box[2]  # 翻转z1
    # rect_width_flip = z_flip_max - z_flip_min
    # rect_sagittal_flip = Rectangle(
    #     (z_flip_min, y_min_sagittal), rect_width_flip, rect_height_sagittal,
    #     linewidth=2, edgecolor='g', facecolor='none'
    # )
    # ax3.add_patch(rect_sagittal_flip)
    
    ax3.set_title(f'Sagittal View (x={x_center})')
    ax3.axis('off')
    
    # 添加置信度信息
    plt.suptitle(f'修正后的method_flip_xy - Nodule #{nodule_idx+1} - Score: {score:.4f}', fontsize=16)
    
    # 保存图像
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"  - 修正后的三平面可视化图像已保存至: {output_file}")
    return True

def fix_method_flip_xy(predictions_file, image_path=None):
    """
    为method_flip_xy方法创建修正版本，解决边界框位置不准确的问题
    
    参数：
        predictions_file: 预测结果JSON文件路径
        image_path: 可选的图像文件路径，如果为None则使用预测结果中的路径
    """
    print_header("修正method_flip_xy方法")
    
    try:
        # 导入必要的函数
        from visualize_nodules_simplified import (
            normalize_to_uint8,
            create_triplanar_visualization
        )
        
        # 创建测试输出目录
        test_output_dir = "analysis_output/fixed_flip_xy"
        os.makedirs(test_output_dir, exist_ok=True)
        print(f"输出目录: {test_output_dir}")
        
        # 加载预测结果
        print(f"加载预测文件: {predictions_file}")
        predictions = load_json_predictions(predictions_file)
        validation_predictions = predictions.get("validation", [])
        
        if not validation_predictions:
            print("预测结果为空，请检查JSON文件")
            return
        
        # 获取图像路径
        pred = validation_predictions[0]
        if image_path is None:
            image_path = pred["image"]
        
        boxes = pred["box"]
        scores = pred["score"]
        
        print(f"使用图像: {image_path}")
        print(f"边界框数量: {len(boxes)}")
        
        # 选择高置信度的边界框
        test_boxes = []
        test_scores = []
        for i, (box, score) in enumerate(zip(boxes, scores)):
            if score >= 0.8 and len(test_boxes) < 3:  # 最多选择3个高置信度的边界框
                test_boxes.append(box)
                test_scores.append(score)
                print(f"选择边界框 #{i+1}: {box}, 置信度: {score:.4f}")
        
        if not test_boxes and len(boxes) > 0:
            # 如果没有高置信度的边界框，使用置信度最高的那个
            max_score_idx = max(range(len(scores)), key=lambda i: scores[i])
            test_boxes.append(boxes[max_score_idx])
            test_scores.append(scores[max_score_idx])
            print(f"使用置信度最高的边界框: {boxes[max_score_idx]}, 置信度: {scores[max_score_idx]:.4f}")
        
        if not test_boxes:
            print("没有可用的边界框进行测试")
            return
        
        # 加载图像数据
        if image_path.lower().endswith(('.nii', '.nii.gz')):
            try:
                import nibabel as nib
                img = nib.load(image_path)
                image_data = img.get_fdata()
                affine = img.affine
                print(f"NIfTI图像形状: {image_data.shape}")
                print(f"仿射矩阵:\n{affine}")
                
                # 测试标准的method_flip_xy方法
                for i, (box, score) in enumerate(zip(test_boxes, test_scores)):
                    print(f"\n处理边界框 #{i+1}:")
                    print(f"原始边界框: {box}")
                    
                    # 应用flip_xy变换
                    flip_xy_box = [
                        image_data.shape[0] - box[3],  # x1 = dim_x - x2
                        image_data.shape[1] - box[4],  # y1 = dim_y - y2
                        box[2],                        # z1
                        image_data.shape[0] - box[0],  # x2 = dim_x - x1
                        image_data.shape[1] - box[1],  # y2 = dim_y - y1
                        box[5]                         # z2
                    ]
                    print(f"flip_xy变换后的边界框: {flip_xy_box}")
                    
                    # 使用标准create_triplanar_visualization创建可视化
                    standard_output = os.path.join(test_output_dir, f"standard_flip_xy_nodule_{i+1}.png")
                    create_triplanar_visualization(
                        image_data,
                        flip_xy_box,
                        score,
                        standard_output,
                        i,
                        lower_bound=-1000,
                        upper_bound=100
                    )
                    print(f"标准可视化已保存: {standard_output}")
                    
                    # 使用修正后的绘制逻辑创建可视化
                    fixed_output = os.path.join(test_output_dir, f"fixed_flip_xy_nodule_{i+1}.png")
                    modify_drawing_logic_for_flip_xy(
                        image_data,
                        flip_xy_box,
                        fixed_output,
                        score,
                        i,
                        lower_bound=-1000,
                        upper_bound=100
                    )
                    print(f"修正后的可视化已保存: {fixed_output}")
                    
                    # 还原测试 - 从flip_xy变换的坐标还原回原始坐标
                    restored_box = [
                        image_data.shape[0] - flip_xy_box[3],  # x1 = dim_x - x2'
                        image_data.shape[1] - flip_xy_box[4],  # y1 = dim_y - y2'
                        flip_xy_box[2],                        # z1
                        image_data.shape[0] - flip_xy_box[0],  # x2 = dim_x - x1'
                        image_data.shape[1] - flip_xy_box[1],  # y2 = dim_y - y1'
                        flip_xy_box[5]                         # z2
                    ]
                    print(f"还原后的边界框: {restored_box}")
                    print(f"原始边界框: {box}")
                    print(f"还原误差: [{restored_box[0]-box[0]}, {restored_box[1]-box[1]}, {restored_box[2]-box[2]}, {restored_box[3]-box[3]}, {restored_box[4]-box[4]}, {restored_box[5]-box[5]}]")
                
                # 创建对比信息文件
                comparison_file = os.path.join(test_output_dir, "comparison_info.txt")
                with open(comparison_file, "w", encoding="utf-8") as f:
                    f.write("method_flip_xy修正对比信息\n")
                    f.write(f"图像路径: {image_path}\n")
                    f.write(f"图像形状: {image_data.shape}\n\n")
                    
                    f.write("问题分析:\n")
                    f.write("1. method_flip_xy方法可以正确显示结节三个平面，但边界框位置不正确\n")
                    f.write("2. 这是因为坐标变换和矩形绘制逻辑不匹配导致的\n\n")
                    
                    f.write("修正方法:\n")
                    f.write("1. 保持flip_xy坐标变换不变，因为它能正确定位结节\n")
                    f.write("2. 修改矩形绘制逻辑，使其与变换后的坐标系统一致\n")
                    f.write("3. 具体修改为在绘制时使用变换后的坐标直接作为矩形的位置，而不是进行额外的坐标映射\n\n")
                    
                    for i, box in enumerate(test_boxes[:3]):
                        f.write(f"边界框 #{i+1}:\n")
                        f.write(f"  原始边界框: {box}\n")
                        
                        flip_xy_box = [
                            image_data.shape[0] - box[3],
                            image_data.shape[1] - box[4],
                            box[2],
                            image_data.shape[0] - box[0],
                            image_data.shape[1] - box[1],
                            box[5]
                        ]
                        f.write(f"  flip_xy变换后: {flip_xy_box}\n\n")
                
                print(f"对比信息已保存至: {comparison_file}")
                print(f"所有可视化结果已保存至: {test_output_dir}")
                print("请比较standard_flip_xy和fixed_flip_xy的结果，验证修正后的方法是否能正确显示边界框位置")
                
            except ImportError as e:
                print(f"导入错误: {e}")
                print("请确保安装了nibabel库")
            except Exception as e:
                print(f"处理NIfTI文件时出错: {e}")
        else:
            print(f"不支持的图像格式: {image_path}")
    
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保可以导入所需的函数")
    except Exception as e:
        print(f"修正method_flip_xy方法时出错: {e}")

def method_flip_xy_with_z_flip(predictions_file, image_path=None):
    """
    测试在flip_xy基础上增加Z轴翻转的方法，尝试解决冠状视图和矢状视图中边界框位置不准确的问题
    
    参数：
        predictions_file: 预测结果JSON文件路径
        image_path: 可选的图像文件路径，如果为None则使用预测结果中的路径
    """
    print_header("测试Z轴翻转的方法")
    
    try:
        # 导入必要的函数
        from visualize_nodules_simplified import (
            normalize_to_uint8,
            create_triplanar_visualization
        )
        
        # 创建测试输出目录
        test_output_dir = "analysis_output/flip_xy_with_z_flip"
        os.makedirs(test_output_dir, exist_ok=True)
        print(f"输出目录: {test_output_dir}")
        
        # 加载预测结果
        print(f"加载预测文件: {predictions_file}")
        predictions = load_json_predictions(predictions_file)
        validation_predictions = predictions.get("validation", [])
        
        if not validation_predictions:
            print("预测结果为空，请检查JSON文件")
            return
        
        # 获取图像路径
        pred = validation_predictions[0]
        if image_path is None:
            image_path = pred["image"]
        
        boxes = pred["box"]
        scores = pred["score"]
        
        print(f"使用图像: {image_path}")
        print(f"边界框数量: {len(boxes)}")
        
        # 选择高置信度的边界框
        test_boxes = []
        test_scores = []
        for i, (box, score) in enumerate(zip(boxes, scores)):
            if score >= 0.8 and len(test_boxes) < 3:  # 最多选择3个高置信度的边界框
                test_boxes.append(box)
                test_scores.append(score)
                print(f"选择边界框 #{i+1}: {box}, 置信度: {score:.4f}")
        
        if not test_boxes and len(boxes) > 0:
            # 如果没有高置信度的边界框，使用置信度最高的那个
            max_score_idx = max(range(len(scores)), key=lambda i: scores[i])
            test_boxes.append(boxes[max_score_idx])
            test_scores.append(scores[max_score_idx])
            print(f"使用置信度最高的边界框: {boxes[max_score_idx]}, 置信度: {scores[max_score_idx]:.4f}")
        
        if not test_boxes:
            print("没有可用的边界框进行测试")
            return
        
        # 加载图像数据
        if image_path.lower().endswith(('.nii', '.nii.gz')):
            try:
                import nibabel as nib
                img = nib.load(image_path)
                image_data = img.get_fdata()
                affine = img.affine
                print(f"NIfTI图像形状: {image_data.shape}")
                
                # 测试四种方法
                methods = [
                    {
                        "name": "flip_xy",
                        "description": "标准的翻转X和Y坐标",
                        "transform": lambda box, shape: [
                            shape[0] - box[3],  # x1 = dim_x - x2
                            shape[1] - box[4],  # y1 = dim_y - y2
                            box[2],             # z1
                            shape[0] - box[0],  # x2 = dim_x - x1
                            shape[1] - box[1],  # y2 = dim_y - y1
                            box[5]              # z2
                        ]
                    },
                    {
                        "name": "flip_xyz",
                        "description": "翻转X、Y和Z坐标",
                        "transform": lambda box, shape: [
                            shape[0] - box[3],  # x1 = dim_x - x2
                            shape[1] - box[4],  # y1 = dim_y - y2
                            shape[2] - box[5],  # z1 = dim_z - z2
                            shape[0] - box[0],  # x2 = dim_x - x1
                            shape[1] - box[1],  # y2 = dim_y - y1
                            shape[2] - box[2]   # z2 = dim_z - z1
                        ]
                    },
                    {
                        "name": "flip_xz",
                        "description": "翻转X和Z坐标",
                        "transform": lambda box, shape: [
                            shape[0] - box[3],  # x1 = dim_x - x2
                            box[1],             # y1
                            shape[2] - box[5],  # z1 = dim_z - z2
                            shape[0] - box[0],  # x2 = dim_x - x1
                            box[4],             # y2
                            shape[2] - box[2]   # z2 = dim_z - z1
                        ]
                    },
                    {
                        "name": "flip_yz",
                        "description": "翻转Y和Z坐标",
                        "transform": lambda box, shape: [
                            box[0],             # x1
                            shape[1] - box[4],  # y1 = dim_y - y2
                            shape[2] - box[5],  # z1 = dim_z - z2
                            box[3],             # x2
                            shape[1] - box[1],  # y2 = dim_y - y1
                            shape[2] - box[2]   # z2 = dim_z - z1
                        ]
                    }
                ]
                
                for method in methods:
                    for i, (box, score) in enumerate(zip(test_boxes, test_scores)):
                        print(f"\n处理边界框 #{i+1}，方法: {method['name']}:")
                        print(f"原始边界框: {box}")
                        
                        # 应用坐标变换
                        transformed_box = method["transform"](box, image_data.shape)
                        print(f"{method['name']}变换后的边界框: {transformed_box}")
                        
                        # 创建自定义可视化
                        output_file = os.path.join(test_output_dir, f"{method['name']}_nodule_{i+1}.png")
                        draw_modified_visualization(
                            image_data,
                            transformed_box,
                            output_file,
                            score,
                            i,
                            method['name'],
                            lower_bound=-1000,
                            upper_bound=100
                        )
                        print(f"{method['name']}方法的可视化已保存: {output_file}")
                
                print(f"所有测试方法的可视化结果已保存至: {test_output_dir}")
                print("请比较这些结果，确定哪种方法能最准确地显示肺结节在所有三个视图中的位置")
                
            except ImportError as e:
                print(f"导入错误: {e}")
                print("请确保安装了nibabel库")
            except Exception as e:
                print(f"处理NIfTI文件时出错: {e}")
        else:
            print(f"不支持的图像格式: {image_path}")
    
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保可以导入所需的函数")
    except Exception as e:
        print(f"测试Z轴翻转方法时出错: {e}")

def draw_modified_visualization(image_data, box, output_file, score, nodule_idx, method_name, lower_bound=-1000, upper_bound=100):
    """
    创建一个适配不同坐标变换方法的三平面可视化函数
    
    参数:
        image_data: 3D图像数据
        box: 已经变换后的边界框坐标 [x1, y1, z1, x2, y2, z2]
        output_file: 输出文件路径
        score: 检测置信度
        nodule_idx: 结节索引
        method_name: 坐标变换方法的名称
        lower_bound: 窗位下限
        upper_bound: 窗位上限
    """
    print(f"\n创建适用于{method_name}方法的三平面可视化:")
    print(f"  - 变换后边界框: {box}")
    
    # 计算边界框中心
    x_center = int((box[0] + box[3]) / 2)
    y_center = int((box[1] + box[4]) / 2)
    z_center = int((box[2] + box[5]) / 2)
    print(f"  - 边界框中心点: ({x_center}, {y_center}, {z_center})")
    
    # 提取三个平面的切片
    axial_slice = image_data[:, :, z_center].T
    coronal_slice = image_data[:, y_center, :].T
    sagittal_slice = image_data[x_center, :, :].T
    
    # 窗位调整和归一化
    if lower_bound is not None and upper_bound is not None:
        axial_slice = np.clip(axial_slice, lower_bound, upper_bound)
        coronal_slice = np.clip(coronal_slice, lower_bound, upper_bound)
        sagittal_slice = np.clip(sagittal_slice, lower_bound, upper_bound)
    
    # 将图像归一化为0-255范围的uint8类型
    axial_slice_norm = normalize_to_uint8(axial_slice)
    coronal_slice_norm = normalize_to_uint8(coronal_slice)
    sagittal_slice_norm = normalize_to_uint8(sagittal_slice)
    
    # 创建图像
    fig = plt.figure(figsize=(15, 5))
    gs = gridspec.GridSpec(1, 3, width_ratios=[1, 1, 1])
    
    # ===== 轴状视图（Axial View - z固定）=====
    ax1 = plt.subplot(gs[0])
    ax1.imshow(axial_slice_norm, cmap='gray')
    
    # 获取图像尺寸
    img_height_axial, img_width_axial = axial_slice_norm.shape
    
    # 轴状视图上的边界框
    x_min_axial = max(0, box[0])
    y_min_axial = max(0, box[1])
    width_axial = min(img_width_axial-1, box[3]) - x_min_axial
    height_axial = min(img_height_axial-1, box[4]) - y_min_axial
    
    rect_axial = Rectangle(
        (x_min_axial, y_min_axial), width_axial, height_axial,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax1.add_patch(rect_axial)
    ax1.set_title(f'Axial View (z={z_center})')
    ax1.axis('off')
    
    # ===== 冠状视图（Coronal View - y固定）=====
    ax2 = plt.subplot(gs[1])
    ax2.imshow(coronal_slice_norm, cmap='gray')
    
    # 冠状视图上的边界框
    img_height_coronal, img_width_coronal = coronal_slice_norm.shape
    
    # 基本方法：z作为x轴，x作为y轴
    if "flip_z" in method_name or method_name in ["flip_xyz", "flip_xz", "flip_yz"]:
        # 如果方法包含Z翻转，我们使用翻转的z坐标
        x_min_coronal = max(0, box[2])
        width_coronal = min(img_width_coronal-1, box[5]) - x_min_coronal
    else:
        # 标准方法：直接使用z坐标
        x_min_coronal = max(0, box[2])
        width_coronal = min(img_width_coronal-1, box[5]) - x_min_coronal
    
    y_min_coronal = max(0, box[0])
    height_coronal = min(img_height_coronal-1, box[3]) - y_min_coronal
    
    rect_coronal = Rectangle(
        (x_min_coronal, y_min_coronal), width_coronal, height_coronal,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax2.add_patch(rect_coronal)
    ax2.set_title(f'Coronal View (y={y_center})')
    ax2.axis('off')
    
    # ===== 矢状视图（Sagittal View - x固定）=====
    ax3 = plt.subplot(gs[2])
    ax3.imshow(sagittal_slice_norm, cmap='gray')
    
    # 矢状视图上的边界框
    img_height_sagittal, img_width_sagittal = sagittal_slice_norm.shape
    
    # 基本方法：z作为x轴，y作为y轴
    if "flip_z" in method_name or method_name in ["flip_xyz", "flip_xz", "flip_yz"]:
        # 如果方法包含Z翻转，我们使用翻转的z坐标
        x_min_sagittal = max(0, box[2])
        width_sagittal = min(img_width_sagittal-1, box[5]) - x_min_sagittal
    else:
        # 标准方法：直接使用z坐标
        x_min_sagittal = max(0, box[2])
        width_sagittal = min(img_width_sagittal-1, box[5]) - x_min_sagittal
    
    y_min_sagittal = max(0, box[1])
    height_sagittal = min(img_height_sagittal-1, box[4]) - y_min_sagittal
    
    rect_sagittal = Rectangle(
        (x_min_sagittal, y_min_sagittal), width_sagittal, height_sagittal,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax3.add_patch(rect_sagittal)
    ax3.set_title(f'Sagittal View (x={x_center})')
    ax3.axis('off')
    
    # 添加置信度信息
    plt.suptitle(f'方法: {method_name} - Nodule #{nodule_idx+1} - Score: {score:.4f}', fontsize=16)
    
    # 保存图像
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"  - 可视化图像已保存至: {output_file}")
    return True

def fix_multi_view_drawing(predictions_file, image_path=None):
    """
    修复多视图边界框绘制，尝试多种方法解决冠状视图和矢状视图的边界框定位问题
    
    参数：
        predictions_file: 预测结果JSON文件路径
        image_path: 可选的图像文件路径，如果为None则使用预测结果中的路径
    """
    print_header("修复多视图边界框绘制")
    
    try:
        # 导入必要的函数
        from visualize_nodules_simplified import (
            normalize_to_uint8,
            create_triplanar_visualization
        )
        
        # 创建测试输出目录
        test_output_dir = "analysis_output/multi_view_fix"
        os.makedirs(test_output_dir, exist_ok=True)
        print(f"输出目录: {test_output_dir}")
        
        # 加载预测结果
        print(f"加载预测文件: {predictions_file}")
        predictions = load_json_predictions(predictions_file)
        validation_predictions = predictions.get("validation", [])
        
        if not validation_predictions:
            print("预测结果为空，请检查JSON文件")
            return
        
        # 获取图像路径
        pred = validation_predictions[0]
        if image_path is None:
            image_path = pred["image"]
        
        boxes = pred["box"]
        scores = pred["score"]
        
        print(f"使用图像: {image_path}")
        print(f"边界框数量: {len(boxes)}")
        
        # 选择高置信度的边界框
        test_boxes = []
        test_scores = []
        for i, (box, score) in enumerate(zip(boxes, scores)):
            if score >= 0.8 and len(test_boxes) < 3:  # 最多选择3个高置信度的边界框
                test_boxes.append(box)
                test_scores.append(score)
                print(f"选择边界框 #{i+1}: {box}, 置信度: {score:.4f}")
        
        if not test_boxes and len(boxes) > 0:
            # 如果没有高置信度的边界框，使用置信度最高的那个
            max_score_idx = max(range(len(scores)), key=lambda i: scores[i])
            test_boxes.append(boxes[max_score_idx])
            test_scores.append(scores[max_score_idx])
            print(f"使用置信度最高的边界框: {boxes[max_score_idx]}, 置信度: {scores[max_score_idx]:.4f}")
        
        if not test_boxes:
            print("没有可用的边界框进行测试")
            return
        
        # 加载图像数据
        if image_path.lower().endswith(('.nii', '.nii.gz')):
            try:
                import nibabel as nib
                img = nib.load(image_path)
                image_data = img.get_fdata()
                affine = img.affine
                print(f"NIfTI图像形状: {image_data.shape}")
                
                # 应用标准的flip_xy变换
                for i, (box, score) in enumerate(zip(test_boxes, test_scores)):
                    print(f"\n处理边界框 #{i+1}:")
                    print(f"原始边界框: {box}")
                    
                    # 应用flip_xy变换
                    flip_xy_box = [
                        image_data.shape[0] - box[3],  # x1 = dim_x - x2
                        image_data.shape[1] - box[4],  # y1 = dim_y - y2
                        box[2],                        # z1
                        image_data.shape[0] - box[0],  # x2 = dim_x - x1
                        image_data.shape[1] - box[1],  # y2 = dim_y - y1
                        box[5]                         # z2
                    ]
                    print(f"flip_xy变换后的边界框: {flip_xy_box}")
                    
                    # 测试不同的绘制方法
                    draw_methods = [
                        {
                            "name": "standard",
                            "description": "标准绘制方法",
                            "z_flip": False,  # 不翻转Z轴
                            "coronal_switch": False,  # 不交换冠状视图的坐标
                            "sagittal_switch": False  # 不交换矢状视图的坐标
                        },
                        {
                            "name": "z_flip",
                            "description": "翻转Z轴坐标",
                            "z_flip": True,  # 翻转Z轴
                            "coronal_switch": False,
                            "sagittal_switch": False
                        },
                        {
                            "name": "coronal_swap",
                            "description": "交换冠状视图XZ坐标",
                            "z_flip": False,
                            "coronal_switch": True,  # 交换冠状视图的坐标
                            "sagittal_switch": False
                        },
                        {
                            "name": "sagittal_swap",
                            "description": "交换矢状视图YZ坐标",
                            "z_flip": False,
                            "coronal_switch": False,
                            "sagittal_switch": True  # 交换矢状视图的坐标
                        },
                        {
                            "name": "z_flip_and_swap",
                            "description": "翻转Z轴并交换两个视图坐标",
                            "z_flip": True,
                            "coronal_switch": True,
                            "sagittal_switch": True
                        },
                        {
                            "name": "invert_all",
                            "description": "翻转所有轴的方向",
                            "z_flip": True,
                            "invert_x": True,
                            "invert_y": True
                        }
                    ]
                    
                    for method in draw_methods:
                        output_file = os.path.join(test_output_dir, f"method_{method['name']}_nodule_{i+1}.png")
                        print(f"\n测试绘制方法 {method['name']}: {method['description']}")
                        
                        draw_multi_view_box(
                            image_data,
                            flip_xy_box,
                            output_file,
                            score,
                            i,
                            method,
                            lower_bound=-1000,
                            upper_bound=100
                        )
                        print(f"测试结果已保存: {output_file}")
                
                print(f"\n所有测试结果已保存至: {test_output_dir}")
                print("请查看这些结果，确定哪种方法能正确显示所有三个视图中的边界框位置")
                
            except ImportError as e:
                print(f"导入错误: {e}")
                print("请确保安装了nibabel库")
            except Exception as e:
                print(f"处理NIfTI文件时出错: {e}")
        else:
            print(f"不支持的图像格式: {image_path}")
    
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保可以导入所需的函数")
    except Exception as e:
        print(f"修复多视图边界框绘制时出错: {e}")

def draw_multi_view_box(image_data, box, output_file, score, nodule_idx, method, lower_bound=-1000, upper_bound=100):
    """
    根据指定的方法绘制多视图边界框
    
    参数:
        image_data: 3D图像数据
        box: 已经变换后的边界框坐标 [x1, y1, z1, x2, y2, z2]
        output_file: 输出文件路径
        score: 检测置信度
        nodule_idx: 结节索引
        method: 绘制方法参数字典
        lower_bound: 窗位下限
        upper_bound: 窗位上限
    """
    print(f"  使用方法 {method['name']} 绘制边界框")
    print(f"  - 变换后边界框: {box}")
    
    # 计算边界框中心
    x_center = int((box[0] + box[3]) / 2)
    y_center = int((box[1] + box[4]) / 2)
    z_center = int((box[2] + box[5]) / 2)
    print(f"  - 边界框中心点: ({x_center}, {y_center}, {z_center})")
    
    # 提取三个平面的切片
    axial_slice = image_data[:, :, z_center].T
    coronal_slice = image_data[:, y_center, :].T
    sagittal_slice = image_data[x_center, :, :].T
    
    # 窗位调整和归一化
    if lower_bound is not None and upper_bound is not None:
        axial_slice = np.clip(axial_slice, lower_bound, upper_bound)
        coronal_slice = np.clip(coronal_slice, lower_bound, upper_bound)
        sagittal_slice = np.clip(sagittal_slice, lower_bound, upper_bound)
    
    # 将图像归一化为0-255范围的uint8类型
    axial_slice_norm = normalize_to_uint8(axial_slice)
    coronal_slice_norm = normalize_to_uint8(coronal_slice)
    sagittal_slice_norm = normalize_to_uint8(sagittal_slice)
    
    # 创建图像
    fig = plt.figure(figsize=(15, 5))
    gs = gridspec.GridSpec(1, 3, width_ratios=[1, 1, 1])
    
    # ===== 轴状视图（Axial View - z固定）=====
    ax1 = plt.subplot(gs[0])
    ax1.imshow(axial_slice_norm, cmap='gray')
    
    # 获取图像尺寸
    img_height_axial, img_width_axial = axial_slice_norm.shape
    
    # 轴状视图上的边界框 - 这个已经正确了，保持不变
    x_min_axial = max(0, box[0])
    y_min_axial = max(0, box[1])
    width_axial = min(img_width_axial-1, box[3]) - x_min_axial
    height_axial = min(img_height_axial-1, box[4]) - y_min_axial
    
    rect_axial = Rectangle(
        (x_min_axial, y_min_axial), width_axial, height_axial,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax1.add_patch(rect_axial)
    ax1.set_title(f'Axial View (z={z_center})')
    ax1.axis('off')
    
    # ===== 冠状视图（Coronal View - y固定）=====
    ax2 = plt.subplot(gs[1])
    ax2.imshow(coronal_slice_norm, cmap='gray')
    
    # 冠状视图上的边界框 - 测试不同的绘制方法
    img_height_coronal, img_width_coronal = coronal_slice_norm.shape
    
    # Z轴处理：根据z_flip参数决定是否翻转Z轴坐标
    if method.get('z_flip', False):
        # 翻转Z轴坐标
        z1 = image_data.shape[2] - box[5]  # z1 = dim_z - z2
        z2 = image_data.shape[2] - box[2]  # z2 = dim_z - z1
    else:
        # 使用原始Z轴坐标
        z1 = box[2]
        z2 = box[5]
    
    # 如果需要交换冠状视图的坐标
    if method.get('coronal_switch', False):
        # 冠状视图中，交换X和Z的映射：X为横坐标，Z为纵坐标
        x_min_coronal = max(0, box[0])
        y_min_coronal = max(0, z1)
        width_coronal = min(img_width_coronal-1, box[3]) - x_min_coronal
        height_coronal = min(img_height_coronal-1, z2) - y_min_coronal
    else:
        # 标准方式：Z为横坐标，X为纵坐标
        x_min_coronal = max(0, z1)
        y_min_coronal = max(0, box[0])
        width_coronal = min(img_width_coronal-1, z2) - x_min_coronal
        height_coronal = min(img_height_coronal-1, box[3]) - y_min_coronal
    
    # 可选的额外坐标反转
    if method.get('invert_x', False) and not method.get('coronal_switch', False):
        x_min_coronal = img_width_coronal - 1 - (x_min_coronal + width_coronal)
    
    rect_coronal = Rectangle(
        (x_min_coronal, y_min_coronal), width_coronal, height_coronal,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax2.add_patch(rect_coronal)
    ax2.set_title(f'Coronal View (y={y_center})')
    ax2.axis('off')
    
    # ===== 矢状视图（Sagittal View - x固定）=====
    ax3 = plt.subplot(gs[2])
    ax3.imshow(sagittal_slice_norm, cmap='gray')
    
    # 矢状视图上的边界框 - 测试不同的绘制方法
    img_height_sagittal, img_width_sagittal = sagittal_slice_norm.shape
    
    # Z轴已经在上面处理过了，这里使用相同的处理结果
    
    # 如果需要交换矢状视图的坐标
    if method.get('sagittal_switch', False):
        # 矢状视图中，交换Y和Z的映射：Y为横坐标，Z为纵坐标
        x_min_sagittal = max(0, box[1])
        y_min_sagittal = max(0, z1)
        width_sagittal = min(img_width_sagittal-1, box[4]) - x_min_sagittal
        height_sagittal = min(img_height_sagittal-1, z2) - y_min_sagittal
    else:
        # 标准方式：Z为横坐标，Y为纵坐标
        x_min_sagittal = max(0, z1)
        y_min_sagittal = max(0, box[1])
        width_sagittal = min(img_width_sagittal-1, z2) - x_min_sagittal
        height_sagittal = min(img_height_sagittal-1, box[4]) - y_min_sagittal
    
    # 可选的额外坐标反转
    if method.get('invert_y', False) and not method.get('sagittal_switch', False):
        y_min_sagittal = img_height_sagittal - 1 - (y_min_sagittal + height_sagittal)
    
    rect_sagittal = Rectangle(
        (x_min_sagittal, y_min_sagittal), width_sagittal, height_sagittal,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax3.add_patch(rect_sagittal)
    ax3.set_title(f'Sagittal View (x={x_center})')
    ax3.axis('off')
    
    # 添加置信度和方法信息
    plt.suptitle(f'方法: {method["name"]} - Nodule #{nodule_idx+1} - Score: {score:.4f}', fontsize=16)
    
    # 保存图像
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"  - 可视化图像已保存至: {output_file}")
    return True

def create_integrated_solution(predictions_file, image_path=None):
    """
    整合多种方法的优点，创建一个能够在所有三个视图中正确显示边界框的解决方案
    
    参数：
        predictions_file: 预测结果JSON文件路径
        image_path: 可选的图像文件路径，如果为None则使用预测结果中的路径
    """
    print_header("整合解决方案")
    
    try:
        # 导入必要的函数
        from visualize_nodules_simplified import (
            normalize_to_uint8,
            create_triplanar_visualization
        )
        
        # 创建输出目录
        output_dir = "analysis_output/integrated_solution"
        os.makedirs(output_dir, exist_ok=True)
        print(f"输出目录: {output_dir}")
        
        # 加载预测结果
        print(f"加载预测文件: {predictions_file}")
        predictions = load_json_predictions(predictions_file)
        validation_predictions = predictions.get("validation", [])
        
        if not validation_predictions:
            print("预测结果为空，请检查JSON文件")
            return
        
        # 获取图像路径
        pred = validation_predictions[0]
        if image_path is None:
            image_path = pred["image"]
        
        boxes = pred["box"]
        scores = pred["score"]
        
        print(f"使用图像: {image_path}")
        print(f"边界框数量: {len(boxes)}")
        
        # 选择高置信度的边界框
        test_boxes = []
        test_scores = []
        for i, (box, score) in enumerate(zip(boxes, scores)):
            if score >= 0.8 and len(test_boxes) < 3:  # 最多选择3个高置信度的边界框
                test_boxes.append(box)
                test_scores.append(score)
                print(f"选择边界框 #{i+1}: {box}, 置信度: {score:.4f}")
        
        if not test_boxes and len(boxes) > 0:
            # 如果没有高置信度的边界框，使用置信度最高的那个
            max_score_idx = max(range(len(scores)), key=lambda i: scores[i])
            test_boxes.append(boxes[max_score_idx])
            test_scores.append(scores[max_score_idx])
            print(f"使用置信度最高的边界框: {boxes[max_score_idx]}, 置信度: {scores[max_score_idx]:.4f}")
        
        if not test_boxes:
            print("没有可用的边界框进行测试")
            return
        
        # 加载图像数据
        if image_path.lower().endswith(('.nii', '.nii.gz')):
            try:
                import nibabel as nib
                img = nib.load(image_path)
                image_data = img.get_fdata()
                affine = img.affine
                print(f"NIfTI图像形状: {image_data.shape}")
                
                # 应用标准的flip_xy变换
                for i, (box, score) in enumerate(zip(test_boxes, test_scores)):
                    print(f"\n处理边界框 #{i+1}:")
                    print(f"原始边界框: {box}")
                    
                    # 应用flip_xy变换
                    flip_xy_box = [
                        image_data.shape[0] - box[3],  # x1 = dim_x - x2
                        image_data.shape[1] - box[4],  # y1 = dim_y - y2
                        box[2],                        # z1
                        image_data.shape[0] - box[0],  # x2 = dim_x - x1
                        image_data.shape[1] - box[1],  # y2 = dim_y - y1
                        box[5]                         # z2
                    ]
                    print(f"flip_xy变换后的边界框: {flip_xy_box}")
                    
                    # 创建整合解决方案
                    output_file = os.path.join(output_dir, f"integrated_solution_nodule_{i+1}.png")
                    draw_integrated_visualization(
                        image_data,
                        flip_xy_box,
                        output_file,
                        score,
                        i,
                        lower_bound=-1000,
                        upper_bound=100
                    )
                    print(f"整合解决方案已保存: {output_file}")
                
                # 创建最终实现函数
                final_implementation_file = os.path.join(output_dir, "final_implementation.py")
                with open(final_implementation_file, "w", encoding="utf-8") as f:
                    f.write("""
# 修正后的create_triplanar_visualization函数
def create_triplanar_visualization_fixed(image_data, box, score, output_file, nodule_idx, lower_bound=None, upper_bound=None):
    \"\"\"
    创建单个结节的三平面可视化，修正了所有视图中的边界框位置
    
    参数:
        image_data: 3D图像数据
        box: 边界框坐标 [x1, y1, z1, x2, y2, z2]，已经过flip_xy变换
        score: 检测置信度
        output_file: 输出文件路径
        nodule_idx: 结节索引
        lower_bound: 窗位下限
        upper_bound: 窗位上限
    \"\"\"
    import numpy as np
    import matplotlib.pyplot as plt
    import matplotlib.gridspec as gridspec
    from matplotlib.patches import Rectangle
    
    print(f"\\n创建结节的三平面可视化图像:")
    print(f"  - 边界框坐标: {box}")
    print(f"  - 检测置信度: {score:.4f}")
    
    # 计算边界框中心
    x_center = int((box[0] + box[3]) / 2)
    y_center = int((box[1] + box[4]) / 2)
    z_center = int((box[2] + box[5]) / 2)
    print(f"  - 边界框中心点: ({x_center}, {y_center}, {z_center})")
    
    # 提取三个平面的切片
    axial_slice = image_data[:, :, z_center].T
    coronal_slice = image_data[:, y_center, :].T
    sagittal_slice = image_data[x_center, :, :].T
    
    # 窗位调整和归一化
    if lower_bound is not None and upper_bound is not None:
        axial_slice = np.clip(axial_slice, lower_bound, upper_bound)
        coronal_slice = np.clip(coronal_slice, lower_bound, upper_bound)
        sagittal_slice = np.clip(sagittal_slice, lower_bound, upper_bound)
    
    # 将图像归一化为0-255范围的uint8类型
    def normalize_to_uint8(data):
        data_min = np.min(data)
        data_max = np.max(data)
        if data_max > data_min:
            normalized = ((data - data_min) / (data_max - data_min) * 255).astype(np.uint8)
        else:
            normalized = np.zeros_like(data, dtype=np.uint8)
        return normalized
    
    axial_slice_norm = normalize_to_uint8(axial_slice)
    coronal_slice_norm = normalize_to_uint8(coronal_slice)
    sagittal_slice_norm = normalize_to_uint8(sagittal_slice)
    
    # 创建图像
    fig = plt.figure(figsize=(15, 5))
    gs = gridspec.GridSpec(1, 3, width_ratios=[1, 1, 1])
    
    # ===== 轴状视图（Axial View - z固定）=====
    ax1 = plt.subplot(gs[0])
    ax1.imshow(axial_slice_norm, cmap='gray')
    
    # 获取图像尺寸
    img_height_axial, img_width_axial = axial_slice_norm.shape
    
    # 轴状视图上的边界框 - 直接使用翻转后的坐标
    x_min_axial = max(0, box[0])
    y_min_axial = max(0, box[1])
    width_axial = min(img_width_axial-1, box[3]) - x_min_axial
    height_axial = min(img_height_axial-1, box[4]) - y_min_axial
    
    rect_axial = Rectangle(
        (x_min_axial, y_min_axial), width_axial, height_axial,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax1.add_patch(rect_axial)
    ax1.set_title(f'Axial View (z={z_center})')
    ax1.axis('off')
    
    # ===== 冠状视图（Coronal View - y固定）=====
    ax2 = plt.subplot(gs[1])
    ax2.imshow(coronal_slice_norm, cmap='gray')
    
    # 冠状视图上的边界框 - 交换X和Z的映射
    img_height_coronal, img_width_coronal = coronal_slice_norm.shape
    
    # 冠状视图中，交换X和Z的映射：X为横坐标，Z为纵坐标
    x_min_coronal = max(0, box[0])
    y_min_coronal = max(0, box[2])
    width_coronal = min(img_width_coronal-1, box[3]) - x_min_coronal
    height_coronal = min(img_height_coronal-1, box[5]) - y_min_coronal
    
    rect_coronal = Rectangle(
        (x_min_coronal, y_min_coronal), width_coronal, height_coronal,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax2.add_patch(rect_coronal)
    ax2.set_title(f'Coronal View (y={y_center})')
    ax2.axis('off')
    
    # ===== 矢状视图（Sagittal View - x固定）=====
    ax3 = plt.subplot(gs[2])
    ax3.imshow(sagittal_slice_norm, cmap='gray')
    
    # 矢状视图上的边界框 - 交换Y和Z的映射
    img_height_sagittal, img_width_sagittal = sagittal_slice_norm.shape
    
    # 矢状视图中，交换Y和Z的映射：Y为横坐标，Z为纵坐标
    x_min_sagittal = max(0, box[1])
    y_min_sagittal = max(0, box[2])
    width_sagittal = min(img_width_sagittal-1, box[4]) - x_min_sagittal
    height_sagittal = min(img_height_sagittal-1, box[5]) - y_min_sagittal
    
    rect_sagittal = Rectangle(
        (x_min_sagittal, y_min_sagittal), width_sagittal, height_sagittal,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax3.add_patch(rect_sagittal)
    ax3.set_title(f'Sagittal View (x={x_center})')
    ax3.axis('off')
    
    # 添加置信度信息
    plt.suptitle(f'Nodule #{nodule_idx+1} - Score: {score:.4f}', fontsize=16)
    
    # 保存图像
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"  - 三平面可视化图像已保存至: {output_file}")
    return True
""")
                
                print(f"\n最终实现代码已保存至: {final_implementation_file}")
                print("请将此函数替换visualize_nodules_simplified.py中的create_triplanar_visualization函数")
                
                # 创建说明文件
                explanation_file = os.path.join(output_dir, "explanation.txt")
                with open(explanation_file, "w", encoding="utf-8") as f:
                    f.write("多视图边界框绘制解决方案说明\n")
                    f.write("==========================\n\n")
                    f.write("问题分析:\n")
                    f.write("1. 原始方法中，轴状视图(Axial)的边界框能正确显示，但冠状视图(Coronal)和矢状视图(Sagittal)的边界框位置不正确\n")
                    f.write("2. 测试发现，coronal_swap方法能使Axial和Coronal视图正确，sagittal_swap方法能使Axial和Sagittal视图正确\n\n")
                    
                    f.write("解决方案:\n")
                    f.write("1. 保持flip_xy坐标变换不变，因为它能正确定位结节\n")
                    f.write("2. 对轴状视图(Axial)，直接使用变换后的坐标\n")
                    f.write("3. 对冠状视图(Coronal)，交换X和Z的映射关系，使X作为横坐标，Z作为纵坐标\n")
                    f.write("4. 对矢状视图(Sagittal)，交换Y和Z的映射关系，使Y作为横坐标，Z作为纵坐标\n\n")
                    
                    f.write("实现细节:\n")
                    f.write("1. 轴状视图: 使用(box[0], box[1])作为矩形左上角坐标\n")
                    f.write("2. 冠状视图: 使用(box[0], box[2])作为矩形左上角坐标\n")
                    f.write("3. 矢状视图: 使用(box[1], box[2])作为矩形左上角坐标\n\n")
                    
                    f.write("使用方法:\n")
                    f.write("1. 将final_implementation.py中的函数替换visualize_nodules_simplified.py中的create_triplanar_visualization函数\n")
                    f.write("2. 确保在调用该函数前已经应用了flip_xy坐标变换\n")
                
                print(f"解决方案说明已保存至: {explanation_file}")
                
            except ImportError as e:
                print(f"导入错误: {e}")
                print("请确保安装了nibabel库")
            except Exception as e:
                print(f"处理NIfTI文件时出错: {e}")
        else:
            print(f"不支持的图像格式: {image_path}")
    
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保可以导入所需的函数")
    except Exception as e:
        print(f"创建整合解决方案时出错: {e}")

def draw_integrated_visualization(image_data, box, output_file, score, nodule_idx, lower_bound=-1000, upper_bound=100):
    """
    创建整合了多种方法优点的可视化，使所有三个视图的边界框都能正确显示
    
    参数:
        image_data: 3D图像数据
        box: 已经变换后的边界框坐标 [x1, y1, z1, x2, y2, z2]
        output_file: 输出文件路径
        score: 检测置信度
        nodule_idx: 结节索引
        lower_bound: 窗位下限
        upper_bound: 窗位上限
    """
    print(f"\n创建整合解决方案的三平面可视化:")
    print(f"  - 变换后边界框: {box}")
    
    # 计算边界框中心
    x_center = int((box[0] + box[3]) / 2)
    y_center = int((box[1] + box[4]) / 2)
    z_center = int((box[2] + box[5]) / 2)
    print(f"  - 边界框中心点: ({x_center}, {y_center}, {z_center})")
    
    # 提取三个平面的切片
    axial_slice = image_data[:, :, z_center].T
    coronal_slice = image_data[:, y_center, :].T
    sagittal_slice = image_data[x_center, :, :].T
    
    # 窗位调整和归一化
    if lower_bound is not None and upper_bound is not None:
        axial_slice = np.clip(axial_slice, lower_bound, upper_bound)
        coronal_slice = np.clip(coronal_slice, lower_bound, upper_bound)
        sagittal_slice = np.clip(sagittal_slice, lower_bound, upper_bound)
    
    # 将图像归一化为0-255范围的uint8类型
    axial_slice_norm = normalize_to_uint8(axial_slice)
    coronal_slice_norm = normalize_to_uint8(coronal_slice)
    sagittal_slice_norm = normalize_to_uint8(sagittal_slice)
    
    # 创建图像
    fig = plt.figure(figsize=(15, 5))
    gs = gridspec.GridSpec(1, 3, width_ratios=[1, 1, 1])
    
    # ===== 轴状视图（Axial View - z固定）=====
    ax1 = plt.subplot(gs[0])
    ax1.imshow(axial_slice_norm, cmap='gray')
    
    # 获取图像尺寸
    img_height_axial, img_width_axial = axial_slice_norm.shape
    
    # 轴状视图上的边界框 - 直接使用翻转后的坐标
    x_min_axial = max(0, box[0])
    y_min_axial = max(0, box[1])
    width_axial = min(img_width_axial-1, box[3]) - x_min_axial
    height_axial = min(img_height_axial-1, box[4]) - y_min_axial
    
    rect_axial = Rectangle(
        (x_min_axial, y_min_axial), width_axial, height_axial,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax1.add_patch(rect_axial)
    ax1.set_title(f'Axial View (z={z_center})')
    ax1.axis('off')
    
    # ===== 冠状视图（Coronal View - y固定）=====
    ax2 = plt.subplot(gs[1])
    ax2.imshow(coronal_slice_norm, cmap='gray')
    
    # 冠状视图上的边界框 - 从coronal_swap方法中获取的绘制逻辑
    img_height_coronal, img_width_coronal = coronal_slice_norm.shape
    
    # 冠状视图中，交换X和Z的映射：X为横坐标，Z为纵坐标
    x_min_coronal = max(0, box[0])
    y_min_coronal = max(0, box[2])
    width_coronal = min(img_width_coronal-1, box[3]) - x_min_coronal
    height_coronal = min(img_height_coronal-1, box[5]) - y_min_coronal
    
    rect_coronal = Rectangle(
        (x_min_coronal, y_min_coronal), width_coronal, height_coronal,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax2.add_patch(rect_coronal)
    ax2.set_title(f'Coronal View (y={y_center})')
    ax2.axis('off')
    
    # ===== 矢状视图（Sagittal View - x固定）=====
    ax3 = plt.subplot(gs[2])
    ax3.imshow(sagittal_slice_norm, cmap='gray')
    
    # 矢状视图上的边界框 - 从sagittal_swap方法中获取的绘制逻辑
    img_height_sagittal, img_width_sagittal = sagittal_slice_norm.shape
    
    # 矢状视图中，交换Y和Z的映射：Y为横坐标，Z为纵坐标
    x_min_sagittal = max(0, box[1])
    y_min_sagittal = max(0, box[2])
    width_sagittal = min(img_width_sagittal-1, box[4]) - x_min_sagittal
    height_sagittal = min(img_height_sagittal-1, box[5]) - y_min_sagittal
    
    rect_sagittal = Rectangle(
        (x_min_sagittal, y_min_sagittal), width_sagittal, height_sagittal,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax3.add_patch(rect_sagittal)
    ax3.set_title(f'Sagittal View (x={x_center})')
    ax3.axis('off')
    
    # 添加置信度信息
    plt.suptitle(f'整合解决方案 - Nodule #{nodule_idx+1} - Score: {score:.4f}', fontsize=16)
    
    # 保存图像
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"  - 整合解决方案的可视化图像已保存至: {output_file}")
    return True

def main():
    parser = argparse.ArgumentParser(description="医学图像坐标系分析工具")
    parser.add_argument("--predictions", type=str, default="ct_detection/output/results.json", help="预测结果JSON文件路径")
    parser.add_argument("--image", type=str, help="图像文件路径(可选)")
    parser.add_argument("--skip-parts", type=str, default="", help="跳过的分析部分，用逗号分隔")
    parser.add_argument("--run-tests", action="store_true", help="运行坐标系变换测试")
    parser.add_argument("--run-enhanced-tests", action="store_true", help="运行增强版坐标系变换测试")
    parser.add_argument("--fix-flip-xy", action="store_true", help="运行method_flip_xy修正测试")
    parser.add_argument("--test-z-flip", action="store_true", help="测试Z轴翻转方法")
    parser.add_argument("--fix-multi-view", action="store_true", help="修复多视图边界框绘制")
    parser.add_argument("--integrated-solution", action="store_true", help="运行整合解决方案")
    
    args = parser.parse_args()
    
    # 如果指定了整合解决方案，则直接运行整合解决方案
    if args.integrated_solution:
        create_integrated_solution(args.predictions, args.image)
        return
    
    # 如果指定了修复多视图，则直接运行多视图边界框绘制修复
    if args.fix_multi_view:
        fix_multi_view_drawing(args.predictions, args.image)
        return
    
    # 如果指定了测试Z轴翻转，则直接运行Z轴翻转测试
    if args.test_z_flip:
        method_flip_xy_with_z_flip(args.predictions, args.image)
        return
    
    # 如果指定了修正method_flip_xy，则直接运行修正测试
    if args.fix_flip_xy:
        fix_method_flip_xy(args.predictions, args.image)
        return
    
    # 如果指定了运行增强版测试，则直接运行增强版坐标系变换测试
    if args.run_enhanced_tests:
        run_enhanced_coordinate_tests(args.predictions, args.image)
        return
    
    # 如果指定了运行测试，则直接运行坐标系变换测试
    if args.run_tests:
        run_coordinate_transform_tests(args.predictions, args.image)
        return
    
    # 确定要跳过的部分
    skip_parts = [part.strip() for part in args.skip_parts.split(",") if part.strip()]
    
    # 创建输出目录
    os.makedirs("analysis_output", exist_ok=True)
    
    # 重定向输出到文件
    original_stdout = sys.stdout
    with open("analysis_output/analysis_results.txt", "w", encoding="utf-8") as f:
        sys.stdout = f
        
        print("医学图像坐标系分析报告")
        print(f"生成时间: {os.popen('date').read().strip()}")
        print(f"脚本版本: 1.0")
        print("\n" + "=" * 80)
        
        # 分析预测结果
        if "predictions" not in skip_parts:
            analyze_prediction_format(args.predictions)
        
        # 分析图像格式
        if "image" not in skip_parts and args.image:
            analyze_image_format(args.image)
        
        # 分析坐标转换
        if "coordinates" not in skip_parts:
            analyze_coordinate_conversion()
        
        # 分析绘制逻辑
        if "drawing" not in skip_parts:
            analyze_drawing_logic()
            
        # 分析可视化脚本
        if "visualization" not in skip_parts:
            analyze_visualization_script(args.predictions)
            
        # 比较可视化方法
        if "comparison" not in skip_parts:
            compare_visualization_methods(args.predictions)
    
    # 恢复标准输出
    sys.stdout = original_stdout
    
    print(f"分析完成! 结果已保存到: analysis_output/analysis_results.txt")
    print(f"测试图像已保存到: analysis_output/drawing_test.png")
    print(f"可视化测试已保存到: analysis_output/viz_test/")
    print(f"如需运行坐标变换测试，请使用 --run-tests 参数")
    print(f"如需运行增强版坐标变换测试，请使用 --run-enhanced-tests 参数")
    print(f"如需修正method_flip_xy方法，请使用 --fix-flip-xy 参数")
    print(f"如需测试Z轴翻转，请使用 --test-z-flip 参数")
    print(f"如需修复多视图边界框绘制，请使用 --fix-multi-view 参数")
    print(f"如需运行整合解决方案，请使用 --integrated-solution 参数")

if __name__ == "__main__":
    main() 