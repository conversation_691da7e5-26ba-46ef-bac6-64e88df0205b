# 肺结节分割与影像组学特征提取使用指南

## 概述

本项目提供了一个完整的肺结节分析流程，分为两个主要部分：

1. **结节分割与三维重建** - 在当前项目中实现
2. **影像组学特征提取** - 在独立项目中实现

这种分离设计避免了依赖冲突，确保每个部分都能稳定运行。

## 第一部分：结节分割与三维重建

### 功能说明

`nodule_segmentation_3d.py` 脚本实现了以下功能：
- 从检测结果中筛选高置信度结节
- 对结节进行精确三维分割
- 生成清晰的结节边界
- 创建三维重建数据
- 保存分割掩码和结节体积数据

### 使用方法

#### 1. 基本使用

```bash
# 使用默认参数
python nodule_segmentation_3d.py

# 指定检测结果文件和输出目录
python nodule_segmentation_3d.py --detection-results "path/to/results.json" --output-dir "output/segmentation"

# 设置置信度阈值
python nodule_segmentation_3d.py --confidence-threshold 0.7

# 选择分割方法
python nodule_segmentation_3d.py --segmentation-method watershed
```

#### 2. 参数说明

- `--detection-results`: 检测结果JSON文件路径（默认：`ct_detection/output/results.json`）
- `--output-dir`: 输出目录路径（默认：`output/segmentation_3d`）
- `--confidence-threshold`: 结节检测置信度阈值（默认：0.5）
- `--segmentation-method`: 分割方法，可选：
  - `adaptive_threshold`: 自适应阈值分割（默认）
  - `watershed`: 分水岭分割
  - `region_growing`: 区域生长分割

#### 3. 输出结果

脚本会在输出目录中创建以下结构：

```
output/segmentation_3d/
├── processing_summary.json          # 处理摘要
├── image1/
│   ├── nodule_1/
│   │   ├── nodule_1_segmentation_mask.nii.gz    # 分割掩码
│   │   ├── nodule_1_volume.nii.gz               # 结节体积
│   │   └── nodule_1_metadata.json               # 元数据
│   └── nodule_2/
│       ├── nodule_2_segmentation_mask.nii.gz
│       ├── nodule_2_volume.nii.gz
│       └── nodule_2_metadata.json
└── image2/
    └── ...
```

### 分割方法说明

#### 1. 自适应阈值分割 (adaptive_threshold)
- **原理**: 基于局部统计信息自动确定分割阈值
- **优点**: 适应性强，对不同密度的结节效果好
- **适用**: 大多数情况下的首选方法

#### 2. 分水岭分割 (watershed)
- **原理**: 基于梯度信息的区域分割
- **优点**: 能够分离粘连的结构
- **适用**: 复杂形状或边界不清晰的结节

#### 3. 区域生长分割 (region_growing)
- **原理**: 从种子点开始，根据相似性准则生长区域
- **优点**: 结果连通性好
- **适用**: 密度均匀的结节

## 第二部分：影像组学特征提取

### 环境准备

#### 1. 创建独立虚拟环境

```bash
# 创建虚拟环境
python -m venv radiomics_env

# 激活虚拟环境
# Windows:
radiomics_env\Scripts\activate
# Linux/macOS:
source radiomics_env/bin/activate

# 安装依赖
pip install -r radiomics_requirements.txt
```

#### 2. 验证安装

```bash
python -c "import radiomics; print('PyRadiomics安装成功')"
python -c "import SimpleITK; print('SimpleITK安装成功')"
```

### 使用方法

#### 1. 基本特征提取

```bash
# 使用默认配置
python radiomics_feature_extractor.py --input-dir "output/segmentation_3d" --output-dir "output/radiomics_features"

# 使用自定义配置
python radiomics_feature_extractor.py --input-dir "output/segmentation_3d" --output-dir "output/radiomics_features" --config "radiomics_config_example.yaml"
```

#### 2. 参数说明

- `--input-dir`: 分割结果输入目录（第一部分的输出）
- `--output-dir`: 特征输出目录
- `--config`: 配置文件路径（可选）

#### 3. 输出结果

```
output/radiomics_features/
├── radiomics_features.csv           # 特征CSV文件
├── radiomics_features.json          # 特征JSON文件
├── feature_description.json         # 特征描述
└── extraction_summary.json          # 提取摘要
```

### 配置文件说明

#### 1. 复制配置文件模板

```bash
cp radiomics_config_example.yaml my_radiomics_config.yaml
```

#### 2. 主要配置项

**图像类型配置**:
```yaml
imageTypes:
  Original: {}                    # 原始图像
  LoG:
    sigma: [2.0, 3.0, 4.0, 5.0]  # 拉普拉斯高斯滤波
  Wavelet:
    wavelet: 'coif1'            # 小波变换
```

**特征类别配置**:
```yaml
featureClasses:
  firstorder: []    # 一阶统计特征
  shape: []         # 形状特征
  glcm: []          # 灰度共生矩阵
  glrlm: []         # 灰度游程矩阵
  glszm: []         # 灰度大小区域矩阵
  ngtdm: []         # 邻域灰度差矩阵
  gldm: []          # 灰度依赖矩阵
```

**处理参数**:
```yaml
processing:
  n_jobs: 4                    # 并行进程数
  quality_check: true          # 质量检查
  min_voxel_count: 10         # 最小体素数
```

## 完整工作流程

### 步骤1：运行检测

首先确保已经运行了肺结节检测，生成了检测结果文件。

### 步骤2：结节分割

```bash
# 在主项目环境中运行
python nodule_segmentation_3d.py \
    --detection-results "ct_detection/output/results.json" \
    --output-dir "output/segmentation_3d" \
    --confidence-threshold 0.6 \
    --segmentation-method adaptive_threshold
```

### 步骤3：准备影像组学环境

```bash
# 创建并激活虚拟环境
python -m venv radiomics_env
radiomics_env\Scripts\activate  # Windows

# 安装依赖
pip install -r radiomics_requirements.txt
```

### 步骤4：特征提取

```bash
# 在影像组学环境中运行
python radiomics_feature_extractor.py \
    --input-dir "output/segmentation_3d" \
    --output-dir "output/radiomics_features" \
    --config "radiomics_config_example.yaml"
```

### 步骤5：结果分析

特征提取完成后，可以使用生成的CSV文件进行进一步分析：

```python
import pandas as pd

# 加载特征数据
features_df = pd.read_csv('../output/radiomics_features/radiomics_features.csv')

# 查看特征概况
print(f"总结节数: {len(features_df)}")
print(f"总特征数: {len(features_df.columns)}")

# 查看特征统计
print(features_df.describe())
```

## 故障排除

### 常见问题

#### 1. 分割结果为空

**可能原因**:
- 置信度阈值设置过高
- 检测结果文件格式不正确
- 图像文件路径错误

**解决方法**:
- 降低置信度阈值
- 检查检测结果文件格式
- 验证图像文件是否存在

#### 2. PyRadiomics安装失败

**可能原因**:
- 缺少编译工具
- Python版本不兼容

**解决方法**:
```bash
# Windows: 安装Visual Studio Build Tools
# 或使用conda安装
conda install -c radiomics pyradiomics
```

#### 3. 特征提取失败

**可能原因**:
- 分割掩码质量差
- 体素数量过少
- 图像和掩码不匹配

**解决方法**:
- 检查分割质量
- 调整`min_voxel_count`参数
- 验证图像和掩码的空间一致性

#### 4. 内存不足

**解决方法**:
- 减少并行进程数(`n_jobs`)
- 分批处理结节
- 增加系统内存

### 日志查看

两个脚本都会输出详细的日志信息，帮助诊断问题：

```bash
# 查看分割日志
python nodule_segmentation_3d.py --detection-results "results.json" 2>&1 | tee segmentation.log

# 查看特征提取日志
python radiomics_feature_extractor.py --input-dir "input" --output-dir "output" 2>&1 | tee extraction.log
```

## 性能优化建议

### 1. 分割性能优化

- 根据数据特点选择合适的分割方法
- 调整边界扩展参数(`margin`)
- 优化形态学操作参数

### 2. 特征提取性能优化

- 合理设置并行进程数
- 选择必要的特征类别
- 使用适当的重采样参数
- 启用质量检查避免无效计算

### 3. 存储优化

- 定期清理临时文件
- 压缩输出文件
- 使用合适的数据格式

## 扩展功能

### 1. 自定义分割方法

可以在`NoduleSegmentation3D`类中添加新的分割方法：

```python
def custom_segmentation(self, roi_data):
    # 实现自定义分割算法
    pass
```

### 2. 自定义特征

可以扩展PyRadiomics配置，添加自定义特征：

```yaml
radiomics:
  settings:
    # 添加自定义参数
    customSetting: value
```

### 3. 批量处理脚本

可以创建批量处理脚本，自动化整个流程：

```bash
#!/bin/bash
# 批量处理脚本示例

# 分割
python nodule_segmentation_3d.py --detection-results "$1" --output-dir "$2/segmentation"

# 切换环境并提取特征
source radiomics_env/bin/activate
python radiomics_feature_extractor.py --input-dir "$2/segmentation" --output-dir "$2/features"
```

## 结果解释

### 分割质量评估

分割结果的质量可以通过以下指标评估：
- 体素数量：反映结节大小
- 平均强度：反映结节密度
- 形状规整性：通过形状特征评估

### 特征解释

影像组学特征可以分为以下类别：
- **形状特征**：描述结节的几何特性
- **一阶特征**：描述强度分布
- **纹理特征**：描述空间模式和异质性

## 引用和参考

如果使用本工具进行研究，请引用相关文献：

1. PyRadiomics: van Griethuysen, J. J. M. et al. Computational Radiomics System to Decode the Radiographic Phenotype. Cancer Research 77, e104–e107 (2017).

2. 相关的肺结节检测和分析文献

## 技术支持

如遇到问题，请：
1. 查看日志文件
2. 检查配置参数
3. 验证输入数据格式
4. 参考故障排除部分