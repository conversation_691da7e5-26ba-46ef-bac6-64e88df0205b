# 肺结节检测与分类系统完整流程图

## 系统总体流程

```mermaid
graph TD
    subgraph 数据准备
        A[CT数据加载] --> B[数据预处理]
        B --> C[数据增强]
    end

    subgraph 结节检测模块
        D[模型加载] --> E[特征提取]
        E --> F[结节候选区域生成]
        F --> G[结节检测与定位]
        G --> H[检测结果输出]
    end

    subgraph 结节分类模块
        I[提取结节区域] --> J[特征工程]
        J --> K[多模态特征融合]
        K --> L[良恶性分类]
        L --> M[分类结果输出]
    end

    subgraph 结果可视化
        N[三平面可视化] --> O[结节标注]
        O --> P[图像生成与输出]
    end

    C --> D
    H --> I
    M --> N

    style 数据准备 fill:#d3f8e2,stroke:#82ca9d
    style 结节检测模块 fill:#ffd8a8,stroke:#ff9a3c
    style 结节分类模块 fill:#a8c6ff,stroke:#3c6cff
    style 结果可视化 fill:#ffc6e6,stroke:#ff3c9d
```

## 详细流程图

### 1. 系统初始化与配置加载

```mermaid
graph TD
    A[系统启动] --> B[加载环境配置 environment.json]
    B --> C[加载检测配置 config_test.json]
    B --> D[加载分类配置 classification_config.json]
    C --> E[设置数据路径与参数]
    D --> F[设置模型参数与阈值]
    E --> G[初始化系统]
    F --> G
```

### 2. 肺结节检测流程

```mermaid
graph TD
    A[加载检测模型] --> B[读取CT影像]
    B --> C[图像预处理]
    C --> D[特征提取与区域提议]
    
    subgraph 多尺度特征提取
        D --> E[基础网络特征提取]
        E --> F1[特征图 P2]
        E --> F2[特征图 P3]
        E --> F3[特征图 P4]
        E --> F4[特征图 P5]
    end
    
    subgraph 检测头处理
        F1 --> G[区域提议网络]
        F2 --> G
        F3 --> G
        F4 --> G
        G --> H[非极大值抑制 NMS]
        H --> I[候选结节筛选]
    end
    
    I --> J[坐标转换]
    J --> K[结果保存 results.json]

    style 多尺度特征提取 fill:#ffecb3,stroke:#ffb300
    style 检测头处理 fill:#bbdefb,stroke:#2196f3
```

### 3. 肺结节分类流程

```mermaid
graph TD
    A[加载分类模型] --> B[读取检测结果]
    B --> C[筛选高置信度结节]
    
    subgraph 结节处理
        C --> D[提取结节区域]
        D --> E[标准化尺寸调整]
        E --> F[特征增强]
    end
    
    subgraph 特征工程与分类
        F --> G[深度特征提取]
        G --> H[多模态特征融合]
        H --> I[良恶性分类]
    end
    
    I --> J[计算恶性概率]
    J --> K[结果整合]
    K --> L[保存分类结果 classification_results.json]

    style 结节处理 fill:#e8f5e9,stroke:#4caf50
    style 特征工程与分类 fill:#e3f2fd,stroke:#1976d2
```

### 4. 可视化与报告生成

```mermaid
graph TD
    A[读取检测与分类结果] --> B[加载原始CT影像]
    
    subgraph 三平面可视化
        B --> C[生成轴状视图]
        B --> D[生成冠状视图]
        B --> E[生成矢状视图]
    end
    
    subgraph 结节标注
        C --> F[标注边界框]
        D --> F
        E --> F
        F --> G[添加良恶性标记]
    end
    
    G --> H[图像美化与调整]
    H --> I[输出可视化结果]
    I --> J[生成标注DICOM/NIFTI]

    style 三平面可视化 fill:#f3e5f5,stroke:#9c27b0
    style 结节标注 fill:#fff3e0,stroke:#ff9800
```

## 数据流图

```mermaid
flowchart TD
    subgraph 输入
        CT[CT影像数据]
    end

    subgraph 处理
        DL[深度学习模型]
        FE[特征提取]
        DP[数据预处理]
        FM[特征融合]
    end

    subgraph 输出
        DR[检测结果]
        CR[分类结果]
        VR[可视化报告]
    end

    CT --> DP
    DP --> DL
    DL --> FE
    FE --> FM
    FM --> DR
    DR --> CR
    CR --> VR

    style 输入 fill:#e3f2fd,stroke:#1565c0
    style 处理 fill:#fff8e1,stroke:#ffa000
    style 输出 fill:#f9fbe7,stroke:#8bc34a
```

## 系统主要组件与功能

1. **数据准备与预处理**
   - CT数据加载与格式转换
   - 数据归一化与窗位调整
   - 数据增强与样本平衡

2. **结节检测模块**
   - 3D特征提取
   - 候选区域生成
   - 非极大值抑制处理
   - 检测结果坐标转换

3. **结节分类模块**
   - 结节区域提取
   - 深度特征工程
   - 多模态特征融合
   - 良恶性分类

4. **可视化与结果展示**
   - 三平面可视化（轴状、冠状、矢状）
   - 结节边界框标注
   - 良恶性标记与颜色编码
   - 标注图像生成与输出

5. **系统集成**
   - 配置管理
   - 批处理与自动化
   - 结果保存与导出
   - 用户界面交互 