#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一的坐标转换工具模块
提供标准化的坐标系转换和边界框处理功能

主要功能：
1. 统一的坐标系转换方法
2. 边界框验证和修正
3. 三视图坐标映射
4. 调试和日志功能

Author: AI Assistant
Date: 2025-01-31
"""

import numpy as np
import logging
from typing import List, Tuple, Optional, Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CoordinateTransformer:
    """坐标转换器类"""
    
    def __init__(self, debug_mode: bool = False):
        """
        初始化坐标转换器
        
        Args:
            debug_mode: 是否启用调试模式
        """
        self.debug_mode = debug_mode
        
    def validate_bbox(self, bbox: List[float], image_shape: Tuple[int, int, int]) -> List[float]:
        """
        验证并修正边界框坐标
        
        Args:
            bbox: 边界框坐标 [x1, y1, z1, x2, y2, z2]
            image_shape: 图像形状 (x_dim, y_dim, z_dim)
            
        Returns:
            修正后的边界框坐标
        """
        if self.debug_mode:
            logger.info(f"验证边界框: {bbox}, 图像形状: {image_shape}")
            
        x1, y1, z1, x2, y2, z2 = bbox
        x_dim, y_dim, z_dim = image_shape
        
        # 确保坐标顺序正确
        if x1 > x2:
            x1, x2 = x2, x1
            if self.debug_mode:
                logger.warning("交换了X坐标顺序")
                
        if y1 > y2:
            y1, y2 = y2, y1
            if self.debug_mode:
                logger.warning("交换了Y坐标顺序")
                
        if z1 > z2:
            z1, z2 = z2, z1
            if self.debug_mode:
                logger.warning("交换了Z坐标顺序")
        
        # 确保坐标在有效范围内
        x1 = max(0, min(x1, x_dim - 1))
        x2 = max(0, min(x2, x_dim - 1))
        y1 = max(0, min(y1, y_dim - 1))
        y2 = max(0, min(y2, y_dim - 1))
        z1 = max(0, min(z1, z_dim - 1))
        z2 = max(0, min(z2, z_dim - 1))
        
        validated_bbox = [x1, y1, z1, x2, y2, z2]
        
        if self.debug_mode:
            logger.info(f"验证后边界框: {validated_bbox}")
            
        return validated_bbox
    
    def flip_xy_transform(self, bbox: List[float], image_shape: Tuple[int, int, int]) -> List[float]:
        """
        应用flip_xy坐标变换（当前项目使用的方法）
        
        Args:
            bbox: 原始边界框坐标 [x1, y1, z1, x2, y2, z2]
            image_shape: 图像形状 (x_dim, y_dim, z_dim)
            
        Returns:
            变换后的边界框坐标
        """
        if self.debug_mode:
            logger.info(f"应用flip_xy变换: {bbox}")
            
        x1, y1, z1, x2, y2, z2 = bbox
        x_dim, y_dim, z_dim = image_shape
        
        # flip_xy变换：翻转X和Y坐标，Z坐标保持不变
        transformed_bbox = [
            x_dim - x2,  # x1 = dim_x - x2
            y_dim - y2,  # y1 = dim_y - y2
            z1,          # z1 保持不变
            x_dim - x1,  # x2 = dim_x - x1
            y_dim - y1,  # y2 = dim_y - y1
            z2           # z2 保持不变
        ]
        
        # 验证变换后的坐标
        validated_bbox = self.validate_bbox(transformed_bbox, image_shape)
        
        if self.debug_mode:
            logger.info(f"flip_xy变换结果: {validated_bbox}")
            
        return validated_bbox
    
    def generate_annotated_transform(self, bbox: List[float], image_shape: Tuple[int, int, int]) -> List[float]:
        """
        应用generate_annotated_images.py中使用的坐标变换方法
        
        Args:
            bbox: 原始边界框坐标 [x1, y1, z1, x2, y2, z2]
            image_shape: 图像形状 (x_dim, y_dim, z_dim)
            
        Returns:
            变换后的边界框坐标
        """
        if self.debug_mode:
            logger.info(f"应用generate_annotated变换: {bbox}")
            
        x1, y1, z1, x2, y2, z2 = bbox
        x_dim, y_dim, z_dim = image_shape
        
        # generate_annotated_images变换：翻转X和Y坐标并减1
        transformed_bbox = [
            x_dim - x1 - 1,  # x1 = dim_x - x1 - 1
            y_dim - y1 - 1,  # y1 = dim_y - y1 - 1
            z1,              # z1 保持不变
            x_dim - x2 - 1,  # x2 = dim_x - x2 - 1
            y_dim - y2 - 1,  # y2 = dim_y - y2 - 1
            z2               # z2 保持不变
        ]
        
        # 验证变换后的坐标
        validated_bbox = self.validate_bbox(transformed_bbox, image_shape)
        
        if self.debug_mode:
            logger.info(f"generate_annotated变换结果: {validated_bbox}")
            
        return validated_bbox
    
    def no_transform(self, bbox: List[float], image_shape: Tuple[int, int, int]) -> List[float]:
        """
        不进行坐标变换，仅验证边界框
        
        Args:
            bbox: 原始边界框坐标 [x1, y1, z1, x2, y2, z2]
            image_shape: 图像形状 (x_dim, y_dim, z_dim)
            
        Returns:
            验证后的边界框坐标
        """
        if self.debug_mode:
            logger.info(f"不进行变换，仅验证: {bbox}")
            
        return self.validate_bbox(bbox, image_shape)
    
    def apply_transform(self, bbox: List[float], image_shape: Tuple[int, int, int], 
                       method: str = 'flip_xy') -> List[float]:
        """
        应用指定的坐标变换方法
        
        Args:
            bbox: 原始边界框坐标 [x1, y1, z1, x2, y2, z2]
            image_shape: 图像形状 (x_dim, y_dim, z_dim)
            method: 变换方法 ('flip_xy', 'generate_annotated', 'none')
            
        Returns:
            变换后的边界框坐标
        """
        if method == 'flip_xy':
            return self.flip_xy_transform(bbox, image_shape)
        elif method == 'generate_annotated':
            return self.generate_annotated_transform(bbox, image_shape)
        elif method == 'none':
            return self.no_transform(bbox, image_shape)
        else:
            raise ValueError(f"未知的变换方法: {method}")

class TriplanarMapper:
    """三视图坐标映射器"""
    
    def __init__(self, debug_mode: bool = False):
        """
        初始化三视图映射器
        
        Args:
            debug_mode: 是否启用调试模式
        """
        self.debug_mode = debug_mode
    
    def get_slice_center(self, bbox: List[float]) -> Tuple[int, int, int]:
        """
        计算边界框的中心点
        
        Args:
            bbox: 边界框坐标 [x1, y1, z1, x2, y2, z2]
            
        Returns:
            中心点坐标 (x_center, y_center, z_center)
        """
        x_center = int((bbox[0] + bbox[3]) / 2)
        y_center = int((bbox[1] + bbox[4]) / 2)
        z_center = int((bbox[2] + bbox[5]) / 2)
        
        if self.debug_mode:
            logger.info(f"边界框中心点: ({x_center}, {y_center}, {z_center})")
            
        return x_center, y_center, z_center
    
    def get_axial_rect_params(self, bbox: List[float]) -> Dict[str, float]:
        """
        获取轴状视图的矩形参数
        
        Args:
            bbox: 边界框坐标 [x1, y1, z1, x2, y2, z2]
            
        Returns:
            矩形参数字典 {'x': x_min, 'y': y_min, 'width': width, 'height': height}
        """
        params = {
            'x': max(0, bbox[0]),
            'y': max(0, bbox[1]),
            'width': max(1, bbox[3] - bbox[0]),
            'height': max(1, bbox[4] - bbox[1])
        }
        
        if self.debug_mode:
            logger.info(f"轴状视图矩形参数: {params}")
            
        return params
    
    def get_coronal_rect_params(self, bbox: List[float]) -> Dict[str, float]:
        """
        获取冠状视图的矩形参数
        
        Args:
            bbox: 边界框坐标 [x1, y1, z1, x2, y2, z2]
            
        Returns:
            矩形参数字典 {'x': x_min, 'y': y_min, 'width': width, 'height': height}
        """
        params = {
            'x': max(0, bbox[0]),      # X坐标作为横坐标
            'y': max(0, bbox[2]),      # Z坐标作为纵坐标
            'width': max(1, bbox[3] - bbox[0]),   # X方向宽度
            'height': max(1, bbox[5] - bbox[2])   # Z方向高度
        }
        
        if self.debug_mode:
            logger.info(f"冠状视图矩形参数: {params}")
            
        return params
    
    def get_sagittal_rect_params(self, bbox: List[float]) -> Dict[str, float]:
        """
        获取矢状视图的矩形参数
        
        Args:
            bbox: 边界框坐标 [x1, y1, z1, x2, y2, z2]
            
        Returns:
            矩形参数字典 {'x': x_min, 'y': y_min, 'width': width, 'height': height}
        """
        params = {
            'x': max(0, bbox[1]),      # Y坐标作为横坐标
            'y': max(0, bbox[2]),      # Z坐标作为纵坐标
            'width': max(1, bbox[4] - bbox[1]),   # Y方向宽度
            'height': max(1, bbox[5] - bbox[2])   # Z方向高度
        }
        
        if self.debug_mode:
            logger.info(f"矢状视图矩形参数: {params}")
            
        return params

def create_unified_transformer(debug_mode: bool = False) -> CoordinateTransformer:
    """
    创建统一的坐标转换器实例
    
    Args:
        debug_mode: 是否启用调试模式
        
    Returns:
        CoordinateTransformer实例
    """
    return CoordinateTransformer(debug_mode=debug_mode)

def create_triplanar_mapper(debug_mode: bool = False) -> TriplanarMapper:
    """
    创建三视图映射器实例
    
    Args:
        debug_mode: 是否启用调试模式
        
    Returns:
        TriplanarMapper实例
    """
    return TriplanarMapper(debug_mode=debug_mode)

# 便捷函数
def transform_bbox(bbox: List[float], image_shape: Tuple[int, int, int], 
                  method: str = 'flip_xy', debug: bool = False) -> List[float]:
    """
    便捷的边界框变换函数
    
    Args:
        bbox: 边界框坐标 [x1, y1, z1, x2, y2, z2]
        image_shape: 图像形状 (x_dim, y_dim, z_dim)
        method: 变换方法
        debug: 是否启用调试
        
    Returns:
        变换后的边界框坐标
    """
    transformer = create_unified_transformer(debug_mode=debug)
    return transformer.apply_transform(bbox, image_shape, method)

def get_triplanar_params(bbox: List[float], debug: bool = False) -> Dict[str, Dict[str, float]]:
    """
    便捷的三视图参数获取函数
    
    Args:
        bbox: 边界框坐标 [x1, y1, z1, x2, y2, z2]
        debug: 是否启用调试
        
    Returns:
        三视图参数字典
    """
    mapper = create_triplanar_mapper(debug_mode=debug)
    
    return {
        'axial': mapper.get_axial_rect_params(bbox),
        'coronal': mapper.get_coronal_rect_params(bbox),
        'sagittal': mapper.get_sagittal_rect_params(bbox),
        'center': dict(zip(['x', 'y', 'z'], mapper.get_slice_center(bbox)))
    }
