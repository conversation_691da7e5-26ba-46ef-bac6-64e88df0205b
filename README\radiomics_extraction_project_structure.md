# 影像组学特征提取项目结构设计

## 项目概述

这是一个独立的影像组学特征提取项目，专门用于处理肺结节三维分割结果并提取PyRadiomics特征。该项目与主检测项目分离，避免依赖冲突。

## 项目结构

```
lung_nodule_radiomics/
├── README.md
├── requirements.txt
├── setup.py
├── config/
│   ├── radiomics_config.yaml
│   └── feature_extraction_params.json
├── src/
│   ├── __init__.py
│   ├── radiomics_extractor.py
│   ├── feature_processor.py
│   ├── data_loader.py
│   └── utils/
│       ├── __init__.py
│       ├── image_utils.py
│       └── validation.py
├── scripts/
│   ├── extract_features.py
│   ├── batch_processing.py
│   └── feature_analysis.py
├── tests/
│   ├── __init__.py
│   ├── test_radiomics_extractor.py
│   └── test_feature_processor.py
├── data/
│   ├── input/
│   ├── output/
│   └── temp/
├── notebooks/
│   ├── feature_exploration.ipynb
│   └── visualization.ipynb
└── docs/
    ├── installation.md
    ├── usage.md
    └── api_reference.md
```

## 环境依赖

### Python版本
- Python 3.8+

### 核心依赖包

```txt
# 影像组学核心库
pyradiomics==3.0.1

# 医学图像处理
nibabel==4.0.2
SimpleITK==2.2.1

# 数据处理和科学计算
numpy==1.24.3
scipy==1.10.1
pandas==2.0.3
scikit-image==0.21.0

# 可视化
matplotlib==3.7.2
seaborn==0.12.2
plotly==5.15.0

# 配置文件处理
PyYAML==6.0.1

# 日志和进度条
tqdm==4.65.0
loguru==0.7.0

# 数据验证
pydantic==2.1.1

# 并行处理
joblib==1.3.1

# 开发和测试
pytest==7.4.0
pytest-cov==4.1.0
black==23.7.0
flake8==6.0.0
```

## 主要功能模块

### 1. 数据加载模块 (data_loader.py)
- 加载分割结果和原始图像
- 数据格式验证
- 批量数据处理

### 2. 影像组学特征提取模块 (radiomics_extractor.py)
- PyRadiomics特征提取
- 特征类别配置
- 提取参数设置

### 3. 特征处理模块 (feature_processor.py)
- 特征标准化和归一化
- 特征选择和过滤
- 特征质量评估

### 4. 工具模块 (utils/)
- 图像处理工具
- 数据验证工具
- 通用辅助函数

## 与主项目的数据交互

### 输入数据格式
主项目输出的分割结果应包含：
1. 分割掩码文件 (*.nii.gz)
2. 结节体积文件 (*.nii.gz)
3. 元数据文件 (*.json)

### 输出数据格式
影像组学项目输出：
1. 特征CSV文件
2. 特征描述JSON文件
3. 质量评估报告
4. 可视化图表

## 使用流程

1. **环境准备**
   ```bash
   cd lung_nodule_radiomics
   pip install -r requirements.txt
   ```

2. **配置设置**
   - 修改 `config/radiomics_config.yaml`
   - 设置输入输出路径

3. **特征提取**
   ```bash
   python scripts/extract_features.py --input-dir /path/to/segmentation/results --output-dir /path/to/features
   ```

4. **批量处理**
   ```bash
   python scripts/batch_processing.py --config config/radiomics_config.yaml
   ```

## 特征类别

### PyRadiomics支持的特征类别：
1. **First Order Statistics** - 一阶统计特征
2. **Shape-based (3D)** - 三维形状特征
3. **Gray Level Co-occurrence Matrix (GLCM)** - 灰度共生矩阵
4. **Gray Level Run Length Matrix (GLRLM)** - 灰度游程矩阵
5. **Gray Level Size Zone Matrix (GLSZM)** - 灰度大小区域矩阵
6. **Neighboring Gray Tone Difference Matrix (NGTDM)** - 邻域灰度差矩阵
7. **Gray Level Dependence Matrix (GLDM)** - 灰度依赖矩阵

## 配置文件示例

### radiomics_config.yaml
```yaml
# PyRadiomics配置
radiomics:
  # 图像类型
  imageTypes:
    Original: {}
    LoG:
      sigma: [2.0, 3.0, 4.0, 5.0]
    Wavelet:
      start_level: 0
      level: 1
      wavelet: 'coif1'
  
  # 特征类别
  featureClasses:
    firstorder: []
    shape: []
    glcm: []
    glrlm: []
    glszm: []
    ngtdm: []
    gldm: []
  
  # 设置参数
  settings:
    binWidth: 25
    resampledPixelSpacing: [1, 1, 1]
    interpolator: 'sitkBSpline'
    normalize: true
    normalizeScale: 100
    removeOutliers: 3

# 处理参数
processing:
  # 并行处理
  n_jobs: 4
  
  # 输出格式
  output_format: ['csv', 'json']
  
  # 质量控制
  quality_check: true
  min_voxel_count: 10
  
  # 日志级别
  log_level: 'INFO'
```

## 安装说明

### 系统要求
- Windows 10/11, macOS 10.15+, 或 Linux
- Python 3.8 或更高版本
- 至少 8GB RAM (推荐 16GB)
- 足够的磁盘空间存储特征数据

### 安装步骤

1. **创建虚拟环境**
   ```bash
   python -m venv radiomics_env
   source radiomics_env/bin/activate  # Linux/macOS
   # 或
   radiomics_env\Scripts\activate  # Windows
   ```

2. **安装依赖**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

3. **验证安装**
   ```bash
   python -c "import radiomics; print('PyRadiomics安装成功')"
   ```

## 注意事项

1. **内存使用**: 大型3D图像可能需要大量内存，建议监控内存使用情况
2. **计算时间**: 特征提取可能耗时较长，特别是使用滤波器时
3. **数据质量**: 确保分割质量良好，小的或不完整的分割可能影响特征质量
4. **版本兼容**: 保持PyRadiomics和SimpleITK版本兼容
5. **路径设置**: 确保所有文件路径正确，支持中文路径

## 性能优化建议

1. **并行处理**: 使用多进程处理多个结节
2. **内存管理**: 及时释放不需要的图像数据
3. **批量处理**: 一次处理多个结节以提高效率
4. **缓存机制**: 缓存中间结果避免重复计算
5. **进度监控**: 使用进度条监控长时间运行的任务