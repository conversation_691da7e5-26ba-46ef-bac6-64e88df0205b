from monai.data import NibabelReader
from monai.transforms import LoadImaged
data = {"image": "D:/LungCT/01/awl.nii.gz"}
loader = LoadImaged(keys=["image"], reader=NibabelReader())
result = loader(data)
print("Result keys:", result.keys())
# 检查是否存在其他元数据键
for key in result.keys():
    if "meta" in key.lower():
        print(f"Found metadata key: {key}")
        print(result[key])
# 如果没有元数据键，检查图像对象是否包含元信息
if hasattr(result["image"], "meta"):
    print("Image meta attribute:", result["image"].meta)
