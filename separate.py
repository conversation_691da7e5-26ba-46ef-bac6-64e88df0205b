import os
import glob
import pydicom
from pathlib import Path
import shutil
import tkinter as tk
from tkinter import filedialog, messagebox
import hashlib

class DicomSeriesSeparator:
    def __init__(self, root):
        self.root = root
        self.root.title("DICOM Series Separator")
        self.root.geometry("600x400")

        # GUI 元素
        self.input_label = tk.Label(root, text="Input DICOM Folder: Not Selected")
        self.input_label.pack(pady=10)

        self.input_button = tk.Button(root, text="Select Input DICOM Folder", command=self.select_input_folder)
        self.input_button.pack(pady=5)

        self.output_label = tk.Label(root, text="Output Base Folder: Not Selected")
        self.output_label.pack(pady=10)

        self.output_button = tk.Button(root, text="Select Output Base Folder", command=self.select_output_folder)
        self.output_button.pack(pady=5)

        self.separate_button = tk.Button(root, text="Separate DICOM Series", command=self.separate_series, state="disabled")
        self.separate_button.pack(pady=20)

        self.status_label = tk.Label(root, text="Status: Waiting for selection...")
        self.status_label.pack(pady=10)

        self.input_folder = None
        self.output_base_folder = None

    def select_input_folder(self):
        self.input_folder = filedialog.askdirectory(title="Select Input DICOM Folder")
        if self.input_folder:
            self.input_label.config(text=f"Input DICOM Folder: {self.input_folder}")
            self.update_separate_button()

    def select_output_folder(self):
        self.output_base_folder = filedialog.askdirectory(title="Select Output Base Folder")
        if self.output_base_folder:
            self.output_label.config(text=f"Output Base Folder: {self.output_base_folder}")
            self.update_separate_button()

    def update_separate_button(self):
        if self.input_folder and self.output_base_folder:
            self.separate_button.config(state="normal")
            self.status_label.config(text="Status: Ready to separate")
        else:
            self.separate_button.config(state="disabled")
            self.status_label.config(text="Status: Waiting for selection...")

    def separate_series(self):
        try:
            self.status_label.config(text="Status: Separating series... Please wait.")
            self.root.update()

            # 确保输出目录存在
            os.makedirs(self.output_base_folder, exist_ok=True)

            # 读取所有DICOM文件
            dicom_files = glob.glob(os.path.join(self.input_folder, "*.dcm"))
            if not dicom_files:
                raise ValueError(f"No DICOM files found in {self.input_folder}")

            print(f"Found {len(dicom_files)} DICOM files.")
            self.status_label.config(text=f"Status: Found {len(dicom_files)} DICOM files. Processing...")
            self.root.update()

            # 按SeriesInstanceUID分组
            series_dict = {}
            for dcm_file in dicom_files:
                try:
                    ds = pydicom.dcmread(dcm_file)
                    series_uid = ds.SeriesInstanceUID
                    if series_uid not in series_dict:
                        series_dict[series_uid] = []
                    series_dict[series_uid].append(dcm_file)
                except Exception as e:
                    print(f"Warning: Could not read metadata from {dcm_file}: {str(e)}")
                    # 如果无法读取元数据，尝试按尺寸分组
                    try:
                        size = (ds.Rows, ds.Columns)
                        size_key = f"Size_{size[0]}_{size[1]}"
                        if size_key not in series_dict:
                            series_dict[size_key] = []
                        series_dict[size_key].append(dcm_file)
                    except:
                        pass

            # 为每个SeriesInstanceUID创建子文件夹并复制文件
            for series_uid, files in series_dict.items():
                # 使用哈希函数生成唯一的文件夹名
                series_folder_name = hashlib.sha256(series_uid.encode()).hexdigest()[:50]
                series_folder = os.path.join(self.output_base_folder, series_folder_name)
                os.makedirs(series_folder, exist_ok=True)

                # 复制文件到对应文件夹
                for src_file in files:
                    dst_file = os.path.join(series_folder, os.path.basename(src_file))
                    shutil.copy2(src_file, dst_file)  # 使用copy2保留元数据
                    print(f"Copied {src_file} to {dst_file}")

                print(f"Series {series_uid}: Moved {len(files)} files to {series_folder}")

            self.status_label.config(text=f"Status: Separation completed! {len(series_dict)} series separated.")
            messagebox.showinfo("Success", f"Separation completed! {len(series_dict)} series have been separated into {self.output_base_folder}")

        except Exception as e:
            self.status_label.config(text=f"Status: Error during separation")
            messagebox.showerror("Error", f"Separation failed: {str(e)}")
            print(f"Error: {str(e)}")

def main():
    root = tk.Tk()
    app = DicomSeriesSeparator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
