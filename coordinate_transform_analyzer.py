#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标系转换分析工具
用于分析和对比不同坐标转换方法的效果

主要功能：
1. 分析现有可视化代码中的坐标转换问题
2. 提供多种坐标转换方法的对比
3. 生成测试可视化结果
4. 提供优化建议

Author: AI Assistant
Date: 2025-01-31
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import matplotlib.gridspec as gridspec

def analyze_coordinate_issues():
    """分析当前坐标系转换中的问题"""
    print("=== 坐标系转换问题分析 ===\n")
    
    print("1. 当前存在的问题：")
    print("   - visualize_nodules.py 和 generate_annotated_images.py 使用不同的坐标转换逻辑")
    print("   - flip_xy 变换在不同文件中实现不一致")
    print("   - 三视图中边界框绘制逻辑混乱")
    print("   - 缺乏统一的坐标验证机制")
    
    print("\n2. 问题根源：")
    print("   - 医学图像坐标系（RAS vs LPS）转换不明确")
    print("   - 图像数据加载时的轴向变换未统一处理")
    print("   - 检测模型输出坐标系与可视化坐标系不匹配")
    print("   - 缺乏边界框有效性验证")
    
    print("\n3. 影响：")
    print("   - 边界框位置显示错误")
    print("   - 不同视图中结节位置不一致")
    print("   - 调试困难，难以定位问题")

def compare_transform_methods():
    """对比不同的坐标转换方法"""
    print("\n=== 坐标转换方法对比 ===\n")
    
    # 示例边界框和图像尺寸
    test_box = [100, 150, 50, 120, 170, 70]  # [x1, y1, z1, x2, y2, z2]
    image_shape = (512, 512, 200)  # (x_dim, y_dim, z_dim)
    
    print(f"测试边界框: {test_box}")
    print(f"图像尺寸: {image_shape}")
    
    methods = {
        "原始坐标": lambda box, shape: box,
        "flip_xy (当前方法)": lambda box, shape: [
            shape[0] - box[3], shape[1] - box[4], box[2],
            shape[0] - box[0], shape[1] - box[1], box[5]
        ],
        "generate_annotated_images方法": lambda box, shape: [
            shape[0] - box[0] - 1, shape[1] - box[1] - 1, box[2],
            shape[0] - box[3] - 1, shape[1] - box[4] - 1, box[5]
        ],
        "完全翻转": lambda box, shape: [
            shape[0] - box[3], shape[1] - box[4], shape[2] - box[5],
            shape[0] - box[0], shape[1] - box[1], shape[2] - box[2]
        ]
    }
    
    print("\n各方法转换结果：")
    for name, method in methods.items():
        try:
            result = method(test_box, image_shape)
            print(f"{name:25}: {result}")
            
            # 计算中心点
            center = [(result[0] + result[3])/2, (result[1] + result[4])/2, (result[2] + result[5])/2]
            print(f"{' '*25}  中心点: ({center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f})")
        except Exception as e:
            print(f"{name:25}: 错误 - {e}")

def generate_coordinate_mapping_guide():
    """生成坐标映射指南"""
    print("\n=== 三视图坐标映射指南 ===\n")
    
    print("标准医学图像坐标系：")
    print("- X轴：左(-) → 右(+)")
    print("- Y轴：后(-) → 前(+)")  
    print("- Z轴：下(-) → 上(+)")
    
    print("\n三视图坐标映射：")
    print("1. 轴状面 (Axial) - 固定Z，显示X-Y平面：")
    print("   - 横坐标：X (左右)")
    print("   - 纵坐标：Y (前后)")
    print("   - 边界框：直接使用 (x1,y1) 到 (x2,y2)")
    
    print("\n2. 冠状面 (Coronal) - 固定Y，显示X-Z平面：")
    print("   - 横坐标：X (左右)")
    print("   - 纵坐标：Z (上下)")
    print("   - 边界框：使用 (x1,z1) 到 (x2,z2)")
    
    print("\n3. 矢状面 (Sagittal) - 固定X，显示Y-Z平面：")
    print("   - 横坐标：Y (前后)")
    print("   - 纵坐标：Z (上下)")
    print("   - 边界框：使用 (y1,z1) 到 (y2,z2)")

def create_test_visualization(box, image_shape, method_name, output_file):
    """创建测试可视化"""
    # 创建模拟图像数据
    image_data = np.random.rand(*image_shape) * 1000 - 500
    
    # 计算中心点
    x_center = int((box[0] + box[3]) / 2)
    y_center = int((box[1] + box[4]) / 2)
    z_center = int((box[2] + box[5]) / 2)
    
    # 确保中心点在有效范围内
    x_center = max(0, min(x_center, image_shape[0] - 1))
    y_center = max(0, min(y_center, image_shape[1] - 1))
    z_center = max(0, min(z_center, image_shape[2] - 1))
    
    # 提取切片
    axial_slice = image_data[:, :, z_center].T
    coronal_slice = image_data[:, y_center, :].T
    sagittal_slice = image_data[x_center, :, :].T
    
    # 创建图像
    fig = plt.figure(figsize=(15, 5))
    gs = gridspec.GridSpec(1, 3, width_ratios=[1, 1, 1])
    
    # 轴状视图
    ax1 = plt.subplot(gs[0])
    ax1.imshow(axial_slice, cmap='gray')
    rect1 = Rectangle((box[0], box[1]), box[3]-box[0], box[4]-box[1], 
                     linewidth=2, edgecolor='red', facecolor='none')
    ax1.add_patch(rect1)
    ax1.set_title(f'Axial (z={z_center})')
    ax1.axis('off')
    
    # 冠状视图
    ax2 = plt.subplot(gs[1])
    ax2.imshow(coronal_slice, cmap='gray')
    rect2 = Rectangle((box[0], box[2]), box[3]-box[0], box[5]-box[2], 
                     linewidth=2, edgecolor='red', facecolor='none')
    ax2.add_patch(rect2)
    ax2.set_title(f'Coronal (y={y_center})')
    ax2.axis('off')
    
    # 矢状视图
    ax3 = plt.subplot(gs[2])
    ax3.imshow(sagittal_slice, cmap='gray')
    rect3 = Rectangle((box[1], box[2]), box[4]-box[1], box[5]-box[2], 
                     linewidth=2, edgecolor='red', facecolor='none')
    ax3.add_patch(rect3)
    ax3.set_title(f'Sagittal (x={x_center})')
    ax3.axis('off')
    
    plt.suptitle(f'坐标转换方法: {method_name}', fontsize=16)
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()

def run_comprehensive_test(output_dir="output/coordinate_analysis"):
    """运行综合测试"""
    print("\n=== 运行综合坐标转换测试 ===\n")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试参数
    test_box = [100, 150, 50, 120, 170, 70]
    image_shape = (256, 256, 100)
    
    # 转换方法
    methods = {
        "原始坐标": lambda box, shape: box,
        "flip_xy": lambda box, shape: [
            shape[0] - box[3], shape[1] - box[4], box[2],
            shape[0] - box[0], shape[1] - box[1], box[5]
        ],
        "优化方法": lambda box, shape: [
            max(0, min(shape[0] - box[3], shape[0] - 1)),
            max(0, min(shape[1] - box[4], shape[1] - 1)),
            max(0, min(box[2], shape[2] - 1)),
            max(0, min(shape[0] - box[0], shape[0] - 1)),
            max(0, min(shape[1] - box[1], shape[1] - 1)),
            max(0, min(box[5], shape[2] - 1))
        ]
    }
    
    print(f"测试边界框: {test_box}")
    print(f"图像尺寸: {image_shape}")
    print(f"输出目录: {output_dir}")
    
    for name, method in methods.items():
        try:
            transformed_box = method(test_box, image_shape)
            print(f"\n{name}: {transformed_box}")
            
            # 创建测试可视化
            output_file = os.path.join(output_dir, f"test_{name.replace(' ', '_')}.png")
            create_test_visualization(transformed_box, image_shape, name, output_file)
            print(f"测试图像已保存: {output_file}")
            
        except Exception as e:
            print(f"{name} 测试失败: {e}")
    
    print(f"\n所有测试完成，结果保存在: {output_dir}")

def provide_optimization_recommendations():
    """提供优化建议"""
    print("\n=== 优化建议 ===\n")
    
    print("1. 统一坐标转换逻辑：")
    print("   - 在所有可视化脚本中使用相同的坐标转换函数")
    print("   - 创建专门的坐标转换模块")
    print("   - 添加坐标系转换的详细文档说明")
    
    print("\n2. 增强边界框验证：")
    print("   - 添加边界框有效性检查")
    print("   - 自动修正超出范围的坐标")
    print("   - 确保 x1<x2, y1<y2, z1<z2")
    
    print("\n3. 改进调试功能：")
    print("   - 添加详细的坐标转换日志")
    print("   - 提供可视化调试模式")
    print("   - 输出中间步骤的坐标信息")
    
    print("\n4. 代码重构建议：")
    print("   - 将坐标转换逻辑提取为独立函数")
    print("   - 使用配置文件管理不同的转换方法")
    print("   - 添加单元测试验证转换正确性")

def main():
    """主函数"""
    print("坐标系转换分析工具")
    print("=" * 50)
    
    analyze_coordinate_issues()
    compare_transform_methods()
    generate_coordinate_mapping_guide()
    run_comprehensive_test()
    provide_optimization_recommendations()
    
    print("\n" + "=" * 50)
    print("分析完成！请查看生成的测试图像以验证不同转换方法的效果。")

if __name__ == "__main__":
    main()
