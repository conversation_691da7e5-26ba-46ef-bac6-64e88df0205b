# 肺结节检测系统 - 项目结构说明

## 项目概述

本项目是一个完整的肺结节检测、分割、分类和分析系统，集成了深度学习模型和影像组学特征提取功能。

## 主要文件结构

### 🎯 GUI界面文件
- **`integrated_lung_detection_gui.py`** - 主GUI程序，集成所有功能
- **`start_gui.py`** - GUI启动脚本
- **`GUI_使用说明.md`** - GUI详细使用说明
- **`example_config.json`** - 配置文件示例

### 🔍 检测模块 (ct_detection/)
- **`test.py`** - 检测测试脚本
- **`training.py`** - 模型训练脚本
- **`testing.py`** - 测试功能脚本
- **`test_data.json`** - 测试数据配置
- **`output/`** - 检测结果输出目录

### 🏷️ 分类模块 (ct_classification/)
- **`test.py`** - 分类测试脚本
- **`training_AUC_StepLR.py`** - 分类模型训练
- **`classification_models.py`** - 分类模型定义
- **`utils.py`** - 工具函数
- **`Model_Genesis_FineTuning/`** - Model Genesis微调
- **`Model_MedicalNet3D_FineTuning/`** - MedicalNet3D微调

### 📊 核心功能脚本
- **`detect_and_classify.py`** - 检测和分类集成脚本
- **`nodule_segmentation_3d_fixed.py`** - 3D结节分割
- **`radiomics_feature_extractor.py`** - 影像组学特征提取
- **`generate_annotated_images.py`** - 生成标注图像
- **`visualize_nodules.py`** - 结节可视化
- **`visualize_nodules_optimized.py`** - 优化的可视化
- **`visualize_classification.py`** - 分类结果可视化
- **`preprocess_images.py`** - 图像预处理
- **`run_detection.py`** - 检测运行脚本
- **`project_manager.py`** - 项目管理工具

### ⚙️ 配置文件
- **`config/`** - 配置文件目录
  - `classification_config.json` - 分类配置
  - `environment.json` - 环境配置
- **`radiomics_config_example.yaml`** - 影像组学配置示例
- **`radiomics_config_lung_nodule.yaml`** - 肺结节影像组学配置
- **`radiomics_requirements.txt`** - 影像组学依赖包

### 📁 输出目录
- **`output/`** - 主输出目录
  - `annotated_images/` - 标注图像
  - `classification_results.json` - 分类结果
  - `radiomics_features/` - 影像组学特征
  - `segmentation_3d_fixed/` - 分割结果
  - `visualizations/` - 可视化结果

### 📚 文档目录
- **`README/`** - 文档目录
  - `README.md` - 主说明文档
  - `USAGE_GUIDE.md` - 使用指南
  - `README_env.MD` - 环境配置说明
  - `AI-Lung-Detection-*.md` - 技术文档系列
  - `改进总结.md` - 改进总结

### 🖼️ 其他目录
- **`NLST_Data_Annotations/`** - NLST数据标注
- **`analysis_output/`** - 分析输出结果
- **`readme_figures/`** - 说明文档图片
- **`运行结果/`** - 运行结果存档

## 使用流程

### 1. 环境准备
```bash
# 激活相应的conda环境
conda activate radiomics_env  # 或 medical_imaging_env
```

### 2. 启动GUI
```bash
python start_gui.py
```

### 3. 功能使用
1. **检测**: 在GUI中配置参数并运行检测
2. **分割**: 基于检测结果进行3D分割
3. **分类**: 进行良恶性分类
4. **特征提取**: 提取影像组学特征
5. **可视化**: 生成结果可视化

## 主要改进

### ✅ 已完成的清理工作
- 删除旧版GUI文件 (`detection_config_gui.py`)
- 删除重复的说明文档
- 删除测试和临时文件
- 删除不必要的脚本文件
- 清理配置文件

### 🎯 当前项目特点
- **统一GUI界面**: 集成所有功能模块
- **模块化设计**: 各功能模块独立且可组合
- **完整工作流**: 从检测到分析的完整流程
- **用户友好**: 直观的操作界面和详细文档
- **配置灵活**: 支持多种配置方式和参数预设

## 依赖环境

### 主要依赖包
- Python 3.7+
- PyTorch
- NumPy
- SimpleITK
- PyRadiomics
- Matplotlib
- tkinter (GUI)

### Conda环境
- `radiomics_env` - 影像组学特征提取环境
- `medical_imaging_env` - 医学图像处理环境

## 注意事项

1. **环境切换**: 不同功能可能需要不同的conda环境
2. **路径配置**: 确保模型文件和数据路径正确
3. **内存管理**: 大批量处理时注意内存使用
4. **结果备份**: 重要结果请及时备份

## 技术支持

如有问题，请参考：
1. `GUI_使用说明.md` - GUI详细使用说明
2. `README/` 目录下的技术文档
3. 各模块的代码注释和文档字符串
