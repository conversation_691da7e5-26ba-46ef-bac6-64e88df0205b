# 预测结果可视化工具使用说明

这个工具用于将AI预测的边界框结果直接映射到原始医学影像数据中，生成带有标记的影像文件，可以在专业的医学影像软件中查看。

## 功能特点

- 支持NIfTI和DICOM格式的输入影像
- 可以选择输出为NIfTI或DICOM格式
- 在原始影像上直接绘制边界框，便于在专业软件中查看
- 可调整置信度阈值，过滤低置信度预测
- 可自定义边界框的颜色值
- 提供图形用户界面(GUI)，便于操作

## 安装依赖

在使用此脚本前，请确保已安装以下Python库：

```bash
pip install numpy nibabel pydicom SimpleITK tqdm tkinter
```

## 使用方法

### 图形界面(GUI)模式

直接运行脚本，无需任何参数，将启动GUI界面：

```bash
python generate_annotated_images.py
```

在GUI界面中：
1. 选择预测结果JSON文件（默认为项目中的results.json）
2. 选择配置文件（默认为项目中的config_test.json）
3. 设置输出目录（默认为当前项目目录下的output/annotated_images）
4. 选择输出格式（NIfTI或DICOM）
5. 调整置信度阈值（0.0-1.0之间）
6. 设置边界框颜色值（0-255之间）
7. 点击"开始处理"按钮

处理过程中的日志将显示在界面底部的文本框中。

### 命令行模式

也可以通过命令行参数运行脚本：

```bash
python generate_annotated_images.py --predictions <预测结果JSON文件> --config <配置文件> --output <输出路径> --format <输出格式>
```

### 参数说明

- `--predictions`: 包含预测结果的JSON文件路径（必需）
- `--config`: 配置文件路径，默认为"../config/config_test.json"
- `--output`: 输出文件或目录路径，默认为"./output/annotated_images"
- `--format`: 输出格式，可选 "nifti" 或 "dicom"，默认为 "nifti"
- `--threshold`: 预测置信度阈值，默认为0.5
- `--color`: 边界框的颜色值，默认为255（白色）

### 示例

1. 使用GUI界面（推荐）：

```bash
python generate_annotated_images.py
```

2. 使用命令行，将NIfTI格式的预测结果保存为NIfTI格式：

```bash
python generate_annotated_images.py --predictions D:/Code/AI-in-Lung-Detection/ct_detection/output/results.json --config D:/Code/AI-in-Lung-Detection/config/config_test.json --output ./output/annotated_images --format nifti
```

3. 使用命令行，将DICOM格式的预测结果保存为DICOM格式：

```bash
python generate_annotated_images.py --predictions D:/Code/AI-in-Lung-Detection/ct_detection/output/results.json --config D:/Code/AI-in-Lung-Detection/config/config_test.json --output ./output/annotated_images --format dicom --threshold 0.7 --color 200
```

## 配置文件和路径说明

工具会自动从以下位置读取数据：

1. **预测结果**：默认从`D:/Code/AI-in-Lung-Detection/ct_detection/output/results.json`读取
2. **配置文件**：默认从`D:/Code/AI-in-Lung-Detection/config/config_test.json`读取
3. **原始影像**：从配置文件中的`validation`字段读取
4. **输出目录**：默认为当前项目目录下的`output/annotated_images`

## 注意事项

1. 输入的JSON文件格式应与模型输出的预测结果格式一致，包含边界框坐标和置信度分数。
2. 如果输入是NIfTI格式但要求输出DICOM格式，脚本会发出警告并默认输出为NIfTI格式。
3. 处理大型DICOM系列时可能需要较长时间，请耐心等待。
4. 输出的DICOM文件会生成新的序列UID和实例UID，以便与原始数据区分。
5. 边界框的颜色值应根据影像的灰度范围选择，通常255（白色）在大多数情况下可见度最佳。
6. GUI界面中可以实时调整参数，更加直观和方便。

## 故障排除

1. 如果遇到内存错误，可能是因为影像数据太大，请尝试在更高配置的机器上运行。
2. 如果输出的边界框不可见，可能是因为颜色值与影像背景相近，请尝试调整颜色值参数。
3. 如果DICOM文件无法在某些查看器中正确显示，可能是因为元数据复制不完整，请尝试使用NIfTI格式输出。
4. 如果GUI界面无法启动，请确保已安装tkinter库。在大多数Python安装中，tkinter是默认包含的。

## 高级用法

对于批量处理多个预测结果，可以结合脚本使用循环或批处理文件：

```bash
for file in /path/to/predictions/*.json; do
    python generate_annotated_images.py --predictions $file --config /path/to/config.json --output /path/to/output --format nifti
done
``` 