#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标优化测试脚本
用于测试和验证坐标转换优化的效果

主要功能：
1. 对比原始方法和优化方法的结果
2. 生成测试可视化图像
3. 验证坐标转换的正确性
4. 提供性能对比报告

Author: AI Assistant
Date: 2025-01-31
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import matplotlib.gridspec as gridspec
from pathlib import Path

# 导入我们的优化模块
from coordinate_utils import CoordinateTransformer, TriplanarMapper, transform_bbox, get_triplanar_params

def setup_test_environment():
    """设置测试环境"""
    # 创建测试输出目录
    test_dirs = [
        "output/test_results",
        "output/test_results/original_method",
        "output/test_results/optimized_method",
        "output/test_results/comparison"
    ]
    
    for dir_path in test_dirs:
        os.makedirs(dir_path, exist_ok=True)
    
    print("测试环境设置完成")

def generate_test_data():
    """生成测试数据"""
    # 模拟不同类型的边界框
    test_cases = [
        {
            "name": "标准结节",
            "bbox": [100, 150, 50, 120, 170, 70],
            "image_shape": (512, 512, 200),
            "description": "位于图像中央的标准大小结节"
        },
        {
            "name": "边缘结节",
            "bbox": [10, 20, 5, 30, 40, 15],
            "image_shape": (512, 512, 200),
            "description": "位于图像边缘的小结节"
        },
        {
            "name": "大结节",
            "bbox": [200, 250, 80, 280, 330, 120],
            "image_shape": (512, 512, 200),
            "description": "较大的结节"
        },
        {
            "name": "异常坐标",
            "bbox": [120, 170, 70, 100, 150, 50],  # x1>x2, y1>y2, z1>z2
            "image_shape": (512, 512, 200),
            "description": "坐标顺序异常的边界框"
        }
    ]
    
    return test_cases

def original_flip_xy_transform(bbox, image_shape):
    """原始的flip_xy变换方法（来自visualize_nodules.py）"""
    x1, y1, z1, x2, y2, z2 = bbox
    x_dim, y_dim, z_dim = image_shape
    
    return [
        x_dim - x2,  # x1 = dim_x - x2
        y_dim - y2,  # y1 = dim_y - y2
        z1,          # z1
        x_dim - x1,  # x2 = dim_x - x1
        y_dim - y1,  # y2 = dim_y - y1
        z2           # z2
    ]

def create_comparison_visualization(test_case, original_bbox, optimized_bbox, output_file):
    """创建对比可视化"""
    image_shape = test_case["image_shape"]
    
    # 创建模拟图像数据
    image_data = np.random.rand(*image_shape) * 1000 - 500
    
    # 计算中心点（使用优化后的边界框）
    x_center = int((optimized_bbox[0] + optimized_bbox[3]) / 2)
    y_center = int((optimized_bbox[1] + optimized_bbox[4]) / 2)
    z_center = int((optimized_bbox[2] + optimized_bbox[5]) / 2)
    
    # 确保中心点在有效范围内
    x_center = max(0, min(x_center, image_shape[0] - 1))
    y_center = max(0, min(y_center, image_shape[1] - 1))
    z_center = max(0, min(z_center, image_shape[2] - 1))
    
    # 提取切片
    axial_slice = image_data[:, :, z_center].T
    coronal_slice = image_data[:, y_center, :].T
    sagittal_slice = image_data[x_center, :, :].T
    
    # 创建对比图像
    fig = plt.figure(figsize=(20, 10))
    gs = gridspec.GridSpec(2, 3, height_ratios=[1, 1])
    
    # 原始方法结果
    for i, (slice_data, view_name, bbox_params) in enumerate([
        (axial_slice, "Axial", (original_bbox[0], original_bbox[1], original_bbox[3]-original_bbox[0], original_bbox[4]-original_bbox[1])),
        (coronal_slice, "Coronal", (original_bbox[0], original_bbox[2], original_bbox[3]-original_bbox[0], original_bbox[5]-original_bbox[2])),
        (sagittal_slice, "Sagittal", (original_bbox[1], original_bbox[2], original_bbox[4]-original_bbox[1], original_bbox[5]-original_bbox[2]))
    ]):
        ax = plt.subplot(gs[0, i])
        ax.imshow(slice_data, cmap='gray')
        
        # 绘制原始方法的边界框
        rect = Rectangle((bbox_params[0], bbox_params[1]), bbox_params[2], bbox_params[3],
                        linewidth=2, edgecolor='red', facecolor='none', label='原始方法')
        ax.add_patch(rect)
        ax.set_title(f'{view_name} - 原始方法')
        ax.axis('off')
        ax.legend()
    
    # 优化方法结果
    triplanar_params = get_triplanar_params(optimized_bbox, debug=False)
    
    for i, (slice_data, view_name, view_key) in enumerate([
        (axial_slice, "Axial", "axial"),
        (coronal_slice, "Coronal", "coronal"),
        (sagittal_slice, "Sagittal", "sagittal")
    ]):
        ax = plt.subplot(gs[1, i])
        ax.imshow(slice_data, cmap='gray')
        
        # 绘制优化方法的边界框
        params = triplanar_params[view_key]
        rect = Rectangle((params['x'], params['y']), params['width'], params['height'],
                        linewidth=2, edgecolor='green', facecolor='none', label='优化方法')
        ax.add_patch(rect)
        ax.set_title(f'{view_name} - 优化方法')
        ax.axis('off')
        ax.legend()
    
    plt.suptitle(f'坐标转换对比: {test_case["name"]}', fontsize=16)
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()

def run_coordinate_tests():
    """运行坐标转换测试"""
    print("=== 开始坐标转换测试 ===\n")
    
    # 设置测试环境
    setup_test_environment()
    
    # 生成测试数据
    test_cases = generate_test_data()
    
    # 创建坐标转换器
    transformer = CoordinateTransformer(debug_mode=True)
    
    results = []
    
    for i, test_case in enumerate(test_cases):
        print(f"\n--- 测试案例 {i+1}: {test_case['name']} ---")
        print(f"描述: {test_case['description']}")
        print(f"原始边界框: {test_case['bbox']}")
        print(f"图像尺寸: {test_case['image_shape']}")
        
        # 原始方法
        try:
            original_result = original_flip_xy_transform(test_case['bbox'], test_case['image_shape'])
            print(f"原始方法结果: {original_result}")
        except Exception as e:
            print(f"原始方法失败: {e}")
            original_result = None
        
        # 优化方法
        try:
            optimized_result = transformer.flip_xy_transform(test_case['bbox'], test_case['image_shape'])
            print(f"优化方法结果: {optimized_result}")
        except Exception as e:
            print(f"优化方法失败: {e}")
            optimized_result = None
        
        # 记录结果
        test_result = {
            "test_case": test_case,
            "original_result": original_result,
            "optimized_result": optimized_result,
            "success": original_result is not None and optimized_result is not None
        }
        results.append(test_result)
        
        # 生成对比可视化
        if test_result["success"]:
            output_file = f"output/test_results/comparison/test_{i+1}_{test_case['name'].replace(' ', '_')}.png"
            create_comparison_visualization(test_case, original_result, optimized_result, output_file)
            print(f"对比图像已保存: {output_file}")
    
    return results

def generate_test_report(results):
    """生成测试报告"""
    print("\n=== 测试报告 ===\n")
    
    successful_tests = sum(1 for r in results if r["success"])
    total_tests = len(results)
    
    print(f"测试总数: {total_tests}")
    print(f"成功测试: {successful_tests}")
    print(f"成功率: {successful_tests/total_tests*100:.1f}%")
    
    print("\n详细结果:")
    for i, result in enumerate(results):
        test_case = result["test_case"]
        print(f"\n{i+1}. {test_case['name']}")
        print(f"   状态: {'✓ 成功' if result['success'] else '✗ 失败'}")
        
        if result["success"]:
            original = result["original_result"]
            optimized = result["optimized_result"]
            
            # 计算差异
            if original and optimized:
                diff = [abs(o - opt) for o, opt in zip(original, optimized)]
                max_diff = max(diff)
                print(f"   最大坐标差异: {max_diff:.2f}")
                
                # 检查是否有显著改进
                original_valid = all(0 <= coord < test_case['image_shape'][i//2] for i, coord in enumerate(original))
                optimized_valid = all(0 <= coord < test_case['image_shape'][i//2] for i, coord in enumerate(optimized))
                
                if optimized_valid and not original_valid:
                    print("   ✓ 优化方法修正了超出范围的坐标")
                elif optimized_valid and original_valid:
                    print("   ✓ 两种方法都产生了有效坐标")
                else:
                    print("   ⚠ 需要进一步检查坐标有效性")
    
    # 保存报告到文件
    report_file = "output/test_results/test_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("坐标转换优化测试报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"测试时间: {np.datetime64('now')}\n")
        f.write(f"测试总数: {total_tests}\n")
        f.write(f"成功测试: {successful_tests}\n")
        f.write(f"成功率: {successful_tests/total_tests*100:.1f}%\n\n")
        
        for i, result in enumerate(results):
            test_case = result["test_case"]
            f.write(f"{i+1}. {test_case['name']}\n")
            f.write(f"   描述: {test_case['description']}\n")
            f.write(f"   原始边界框: {test_case['bbox']}\n")
            f.write(f"   图像尺寸: {test_case['image_shape']}\n")
            f.write(f"   原始方法结果: {result['original_result']}\n")
            f.write(f"   优化方法结果: {result['optimized_result']}\n")
            f.write(f"   状态: {'成功' if result['success'] else '失败'}\n\n")
    
    print(f"\n详细报告已保存至: {report_file}")

def main():
    """主函数"""
    print("坐标转换优化测试工具")
    print("=" * 50)
    
    # 运行测试
    results = run_coordinate_tests()
    
    # 生成报告
    generate_test_report(results)
    
    print("\n" + "=" * 50)
    print("测试完成！请查看 output/test_results/ 目录中的结果。")
    print("对比图像显示了原始方法（红色）和优化方法（绿色）的差异。")

if __name__ == "__main__":
    main()
