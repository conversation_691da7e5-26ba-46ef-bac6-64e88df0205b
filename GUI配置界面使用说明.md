# 肺结节检测配置GUI界面使用说明

## 🎯 **界面概览**

GUI界面提供了直观的图形化配置方式，替代了复杂的JSON文件手动编辑，让检测任务配置变得简单易用。

### **主要功能区域**
1. **文件路径配置** - 选择模型、数据和输出路径
2. **检测参数配置** - 调整检测算法参数
3. **操作按钮区** - 验证、保存、加载和运行功能
4. **实时日志显示** - 查看操作过程和运行状态

## 🚀 **快速开始**

### **启动GUI界面**

**方法一：使用批处理文件（推荐）**
```bash
# 双击运行
启动检测配置GUI.bat
```

**方法二：命令行启动**
```bash
# 激活环境
conda activate medical_imaging_env

# 运行GUI
python detection_config_gui.py
```

## 📋 **详细使用步骤**

### **步骤1：文件路径配置**

#### **1.1 预训练模型路径**
- 点击"浏览"按钮选择预训练模型文件（.pt格式）
- 推荐使用LUNA16_mD.pt模型
- 模型下载地址：[Zenodo](https://zenodo.org/records/14967976)

#### **1.2 测试数据列表**
- **选项A**：点击"浏览"选择现有的JSON文件
- **选项B**：点击"创建测试数据列表"自动生成
  - 先选择图像数据目录
  - 系统会自动扫描NIfTI文件并生成列表

#### **1.3 图像数据目录**
- 点击"浏览"选择包含CT图像的目录
- 支持.nii和.nii.gz格式

#### **1.4 结果输出路径**
- 点击"浏览"选择结果保存位置
- 或点击"设置默认输出路径"使用默认位置

### **步骤2：检测参数配置**

#### **2.1 核心参数**
- **置信度阈值** (score_thresh)：
  - 默认值：0.02
  - 越低检测越敏感，越高精确度越高
  - 建议范围：0.01-0.05

- **NMS阈值** (nms_thresh)：
  - 默认值：0.22
  - 控制重叠检测的去除程度
  - 建议范围：0.15-0.3

- **批处理大小** (batch_size)：
  - 默认值：1
  - GPU内存不足时保持为1

#### **2.2 图像块大小**
- **训练块大小**：
  - 默认：192×192×80
  - 影响训练时的内存占用

- **验证块大小**：
  - 默认：512×512×208
  - 影响推理时的内存占用和精度
  - GPU内存不足时可调整为384×384×160

### **步骤3：配置验证和保存**

#### **3.1 验证配置**
- 点击"验证配置"按钮
- 系统会检查：
  - 所有必需路径是否设置
  - 文件是否存在
  - 数据列表格式是否正确
  - 图像文件是否可访问

#### **3.2 保存配置**
- 点击"保存配置"按钮
- 自动生成两个配置文件：
  - `config/environment.json` - 环境配置
  - `ct_detection/DukeLungRADS_BaseModel_epoch300_patch192x192y80z/training_config.json` - 训练配置

#### **3.3 加载配置**
- 点击"加载配置"按钮
- 从现有配置文件中恢复设置

### **步骤4：运行检测任务**

#### **4.1 启动检测**
- 点击"运行检测"按钮
- 系统会：
  - 自动验证和保存配置
  - 确认运行对话框
  - 在后台启动检测任务

#### **4.2 监控进度**
- 实时日志显示运行状态
- 显示检测进度和中间结果
- 出现错误时显示详细信息

#### **4.3 查看结果**
- 检测完成后自动显示结果摘要
- 包含：
  - 处理的图像数量
  - 检测到的结节总数
  - 每个图像的详细统计

## 🎛️ **高级功能**

### **参数调优指南**

#### **提高检测敏感度**
```
置信度阈值: 0.01
NMS阈值: 0.15
验证块大小: 512×512×208
```

#### **提高检测精确度**
```
置信度阈值: 0.05
NMS阈值: 0.3
验证块大小: 384×384×160
```

#### **GPU内存优化**
```
批处理大小: 1
验证块大小: 384×384×160 或更小
```

### **日志管理**
- **清空日志**：清除当前显示的所有日志
- **保存日志**：将日志内容保存到文本文件
- 日志包含详细的操作记录和错误信息

### **结果后处理**
检测完成后，可以直接从结果窗口：
- **打开结果文件**：查看JSON格式的检测结果
- **运行可视化**：自动启动可视化程序显示检测结果

## 🔧 **配置文件说明**

### **环境配置文件** (`config/environment.json`)
```json
{
  "model_path": "模型文件路径",
  "data_list_file_path": "测试数据列表路径",
  "data_base_dir": "图像数据目录",
  "result_list_file_path": "结果输出路径"
}
```

### **训练配置文件** (`training_config.json`)
包含所有检测算法参数，如置信度阈值、NMS阈值、图像块大小等。

## 🚨 **常见问题解决**

### **问题1：GUI无法启动**
**可能原因：**
- conda环境未激活
- 缺少tkinter库

**解决方案：**
```bash
conda activate medical_imaging_env
conda install tk
```

### **问题2：模型文件选择后显示错误**
**可能原因：**
- 模型文件损坏
- 文件路径包含中文字符

**解决方案：**
- 重新下载模型文件
- 将模型文件移动到英文路径

### **问题3：创建测试数据列表失败**
**可能原因：**
- 目录中没有NIfTI文件
- 文件权限不足

**解决方案：**
- 检查目录中是否有.nii或.nii.gz文件
- 确保有写入权限

### **问题4：检测任务运行失败**
**可能原因：**
- GPU内存不足
- 图像格式不支持
- 模型与数据不匹配

**解决方案：**
- 减小验证块大小
- 检查图像预处理
- 验证模型文件完整性

### **问题5：结果显示异常**
**可能原因：**
- 输出目录权限不足
- 磁盘空间不足

**解决方案：**
- 检查输出目录权限
- 确保有足够磁盘空间

## 📊 **性能优化建议**

### **内存优化**
- 使用较小的验证块大小
- 设置批处理大小为1
- 关闭不必要的程序释放内存

### **速度优化**
- 使用GPU加速
- 适当增大验证块大小
- 使用SSD存储数据

### **精度优化**
- 使用较大的验证块大小
- 调整置信度阈值
- 确保图像预处理正确

## 📝 **最佳实践**

1. **首次使用**：
   - 使用默认参数开始
   - 先用单个图像测试
   - 验证结果正确性

2. **批量处理**：
   - 使用"创建测试数据列表"功能
   - 监控内存和磁盘使用情况
   - 定期保存中间结果

3. **参数调优**：
   - 基于验证结果调整参数
   - 记录不同参数的效果
   - 使用日志功能跟踪变化

4. **结果验证**：
   - 使用可视化功能检查结果
   - 对比不同参数设置的效果
   - 保存最佳配置供后续使用

## 🎉 **总结**

GUI配置界面大大简化了肺结节检测任务的配置过程：

- **直观易用**：图形化界面替代复杂的JSON编辑
- **自动验证**：实时检查配置的正确性
- **一键运行**：集成了完整的检测流程
- **实时监控**：提供详细的运行日志
- **结果展示**：自动分析和显示检测结果

通过这个GUI界面，即使是初学者也能轻松配置和运行肺结节检测任务！
