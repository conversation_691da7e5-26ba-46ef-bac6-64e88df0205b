import os
import glob
import re
import tkinter as tk
from tkinter import filedialog, messagebox
import SimpleIT<PERSON> as sitk
import numpy as np
import nibabel as nib
from pathlib import Path
import pydicom
import json

class DicomToNiftiConverter:
    def __init__(self, root):
        self.root = root
        self.root.title("DICOM to NIfTI Converter for Lung Nodule Detection")
        self.root.geometry("600x400")

        self.dicom_label = tk.Label(root, text="DICOM Folder: Not Selected")
        self.dicom_label.pack(pady=10)

        self.dicom_button = tk.Button(root, text="Select DICOM Folder", command=self.select_dicom_folder)
        self.dicom_button.pack(pady=5)

        self.nifti_label = tk.Label(root, text="NIfTI Output Path: Not Selected")
        self.nifti_label.pack(pady=10)

        self.nifti_button = tk.Button(root, text="Select NIfTI Output Path", command=self.select_nifti_path)
        self.nifti_button.pack(pady=5)

        self.convert_button = tk.Button(root, text="Convert DICOM to NIfTI", command=self.convert, state="disabled")
        self.convert_button.pack(pady=20)

        self.status_label = tk.Label(root, text="Status: Waiting for selection...")
        self.status_label.pack(pady=10)

        self.dicom_folder = None
        self.nifti_output_path = None

        # 在界面中添加坐标系选项
        self.coord_var = tk.StringVar(value="LPS")
        self.coord_frame = tk.Frame(root)
        self.coord_frame.pack(pady=10)
        self.coord_label = tk.Label(self.coord_frame, text="坐标系:")
        self.coord_label.pack(side=tk.LEFT)
        self.coord_lps = tk.Radiobutton(self.coord_frame, text="LPS (保留原始)", variable=self.coord_var, value="LPS")
        self.coord_lps.pack(side=tk.LEFT)
        self.coord_ras = tk.Radiobutton(self.coord_frame, text="RAS (转换)", variable=self.coord_var, value="RAS")
        self.coord_ras.pack(side=tk.LEFT)

    def select_dicom_folder(self):
        self.dicom_folder = filedialog.askdirectory(title="Select DICOM Folder")
        if self.dicom_folder:
            self.dicom_label.config(text=f"DICOM Folder: {self.dicom_folder}")
            self.update_convert_button()

    def select_nifti_path(self):
        self.nifti_output_path = filedialog.asksaveasfilename(
            title="Select NIfTI Output Path",
            defaultextension=".nii.gz",
            filetypes=[("NIfTI files", "*.nii.gz"), ("NIfTI files (uncompressed)", "*.nii")]
        )
        if self.nifti_output_path:
            self.nifti_label.config(text=f"NIfTI Output Path: {self.nifti_output_path}")
            self.update_convert_button()

    def update_convert_button(self):
        if self.dicom_folder and self.nifti_output_path:
            self.convert_button.config(state="normal")
            self.status_label.config(text="Status: Ready to convert")
        else:
            self.convert_button.config(state="disabled")
            self.status_label.config(text="Status: Waiting for selection...")

    def extract_image_number(self, filename):
        """
        从文件名中提取图像号（ImgXXX部分中的数字）
        假设格式为 xxx.SeqX.SerX.ImgXXX.dcm
        """
        basename = os.path.basename(filename)
        parts = basename.split('.')
        for part in parts:
            if part.startswith('Img'):
                try:
                    return int(part.replace('Img', ''))
                except ValueError:
                    return 0  # 如果无法提取数字，返回0
        return 0  # 如果未找到Img部分，返回0

    def convert(self):
        try:
            self.status_label.config(text="Status: Converting... Please wait.")
            self.root.update()

            # 获取DICOM文件列表并按图像号排序
            dicom_files = glob.glob(os.path.join(self.dicom_folder, "*.dcm"))
            if not dicom_files:
                raise ValueError(f"No DICOM files found in {self.dicom_folder}")

            # 按文件名中的图像号排序
            dicom_files = sorted(dicom_files, key=self.extract_image_number)
            print(f"Sorted {len(dicom_files)} DICOM files based on image number.")
            # 添加日志：记录 DICOM 文件数量和路径
            print(f"Log - DICOM Files: Found {len(dicom_files)} files in {self.dicom_folder}")
            print(f"Log - First DICOM File: {dicom_files[0]}")

            # 手动读取每个DICOM切片并堆叠为3D体积
            first_image = sitk.ReadImage(dicom_files[0])
            size = first_image.GetSize()
            depth = len(dicom_files)
            array = np.zeros((depth, size[1], size[0]), dtype=np.float32)
            # 添加日志：记录原始 DICOM 的空间分辨率、大小和方向
            print(f"Log - Original DICOM Spacing: {first_image.GetSpacing()}")
            print(f"Log - Original DICOM Size: {first_image.GetSize()}")
            print(f"Log - Original DICOM Direction: {first_image.GetDirection()}")
            print(f"Log - Original DICOM Origin: {first_image.GetOrigin()}")

            for i, dcm_file in enumerate(dicom_files):
                slice_img = sitk.ReadImage(dcm_file)
                array[i, :, :] = sitk.GetArrayFromImage(slice_img)[0, :, :]

            # 转换为SimpleITK图像
            image = sitk.GetImageFromArray(array)
            # 设置层间距（从DICOM元数据中提取）
            image.SetSpacing(first_image.GetSpacing() + (1.0,))
            image.SetOrigin(first_image.GetOrigin())  # 确保保留原始原点
            # 添加日志：记录原始强度范围
            print(
                f"Log - Original Intensity Range: Min={np.min(array):.2f}, Max={np.max(array):.2f}, Mean={np.mean(array):.2f}, Std={np.std(array):.2f}")

            # 在设置方向矩阵之前添加验证
            def validate_direction_matrix(direction):
                """验证方向矩阵是否有效"""
                if len(direction) != 9:
                    return False
                
                # 检查是否接近正交矩阵（列向量互相垂直）
                # 提取三个列向量
                col1 = np.array(direction[0:3])
                col2 = np.array(direction[3:6])
                col3 = np.array(direction[6:9])
                
                # 计算点积，检查是否接近垂直
                dot12 = np.abs(np.dot(col1, col2))
                dot13 = np.abs(np.dot(col1, col3))
                dot23 = np.abs(np.dot(col2, col3))
                
                # 如果列向量接近垂直
                return dot12 < 1e-6 and dot13 < 1e-6 and dot23 < 1e-6

            # 修正方向矩阵设置，确保长度为9（3x3矩阵）
            direction = first_image.GetDirection()
            if validate_direction_matrix(direction):
                image.SetDirection(direction)
            else:
                # 如果方向矩阵长度不正确，使用默认方向矩阵（单位矩阵）
                image.SetDirection((1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))
                print("Warning: Invalid direction matrix length, using default identity matrix.")

            # 修改：不进行LPS到RAS的转换，交给后续的testing.py和generate_transforms.py处理
            convert_to_ras = self.coord_var.get() == "RAS"
            if convert_to_ras:
                # 执行LPS到RAS的转换
                print("Log - Converting from LPS to RAS coordinate system")
                # 修改方向矩阵进行转换
                # 注意：这里需要实现具体的转换逻辑
            else:
                print("Log - Keeping original LPS coordinate system")

            # 重新采样到目标分辨率（基于 README.md 和 training_config.json）
            target_spacing = [0.703125, 0.703125, 1.25]
            size = image.GetSize()
            new_size = [
                int(round(size[0] * image.GetSpacing()[0] / target_spacing[0])),
                int(round(size[1] * image.GetSpacing()[1] / target_spacing[1])),
                int(round(size[2] * image.GetSpacing()[2] / target_spacing[2]))
            ]
            resampler = sitk.ResampleImageFilter()
            resampler.SetReferenceImage(image)
            resampler.SetOutputSpacing(target_spacing)
            resampler.SetSize(new_size)
            resampler.SetInterpolator(sitk.sitkLinear)
            image = resampler.Execute(image)
            # 添加日志：记录重采样参数
            print(f"Log - Resampling: Target Spacing={target_spacing}")
            print(f"Log - Resampling: Original Size={size}, New Size={new_size}")
            print(f"Log - Resampling: New Spacing={image.GetSpacing()}")

            # 修改：强度裁剪范围调整为与testing.py一致（-1024到300 HU），不进行标准化
            array = sitk.GetArrayFromImage(image)
            array = np.clip(array, -1024, 300)  # 与testing.py的ScaleIntensityRanged范围一致
            # 添加日志：记录裁剪后的强度范围
            print(f"Log - After Clipping: Intensity Range: Min={np.min(array):.2f}, Max={np.max(array):.2f}")

            # 修改：不进行强度标准化（均值0，标准差1），交给testing.py的ScaleIntensityRanged处理
            print("Log - Intensity Normalization Skipped: Normalization to mean=0 and std=1 is handled by downstream scripts (testing.py).")

            # 转换回 SimpleITK 图像
            image = sitk.GetImageFromArray(array)
            image.SetSpacing(target_spacing)
            image.SetOrigin(first_image.GetOrigin())  # 再次确保保留原始原点
            image.SetDirection(first_image.GetDirection() if validate_direction_matrix(first_image.GetDirection()) else (1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))

            # 保存为NIfTI
            os.makedirs(os.path.dirname(self.nifti_output_path), exist_ok=True)
            # 添加日志：记录最终图像属性
            print(f"Log - Final NIfTI Spacing: {image.GetSpacing()}")
            print(f"Log - Final NIfTI Size: {image.GetSize()}")
            print(f"Log - Final NIfTI Direction: {image.GetDirection()}")
            print(f"Log - Final NIfTI Origin: {image.GetOrigin()}")
            print(f"Log - Saving NIfTI to: {self.nifti_output_path}")

            # 尝试从原始DICOM文件中提取更多元数据
            series_uid = "unknown_series"
            study_uid = "unknown_study"
            patient_id = "unknown_patient"
            image_position = "unknown_position"
            slice_thickness = "unknown_thickness"

            for dcm_file in dicom_files[:5]:  # 只读取前几个文件获取元数据
                try:
                    ds = pydicom.dcmread(dcm_file)
                    series_uid = ds.SeriesInstanceUID
                    study_uid = ds.StudyInstanceUID
                    patient_id = ds.PatientID
                    if hasattr(ds, 'ImagePositionPatient'):
                        image_position = str(ds.ImagePositionPatient)
                    if hasattr(ds, 'SliceThickness'):
                        slice_thickness = str(ds.SliceThickness)
                    break
                except Exception as e:
                    continue  # 如果出错则跳过

            # 设置SimpleITK图像的元数据
            image.SetMetaData("DICOM.SeriesInstanceUID", series_uid)
            image.SetMetaData("DICOM.StudyInstanceUID", study_uid)
            image.SetMetaData("DICOM.PatientID", patient_id)
            image.SetMetaData("DICOM.ImagePositionPatient", image_position)
            image.SetMetaData("DICOM.SliceThickness", slice_thickness)
            # 添加坐标系信息
            image.SetMetaData("CoordinateSystem", "LPS")  # 明确标记使用LPS坐标系

            # 将坐标系信息添加到日志
            print(f"Log - Coordinate System: LPS (Left-Posterior-Superior)")
            print(f"Log - Note: Downstream processing will convert LPS to RAS if needed")

            # 在保存NIfTI之前，保存更多DICOM元数据以便调试
            dicom_metadata = {
                "SeriesInstanceUID": series_uid,
                "StudyInstanceUID": study_uid,
                "PatientID": patient_id,
                "ImagePositionPatient": image_position,
                "SliceThickness": slice_thickness,
                "OriginalSpacing": [float(x) for x in first_image.GetSpacing()],
                "OriginalSize": [int(x) for x in first_image.GetSize()],
                "OriginalDirection": [float(x) for x in first_image.GetDirection()],
                "OriginalOrigin": [float(x) for x in first_image.GetOrigin()],
                "TargetSpacing": target_spacing,
                "ResampledSize": new_size,
                "CoordinateSystem": "LPS"
            }

            # 保存元数据到JSON文件
            metadata_path = self.nifti_output_path.replace('.nii.gz', '_metadata.json')
            with open(metadata_path, 'w') as f:
                json.dump(dicom_metadata, f, indent=4)
            print(f"Log - Metadata saved to: {metadata_path}")

            # 保存为NIfTI
            sitk.WriteImage(image, self.nifti_output_path)
            self.status_label.config(text=f"Status: Conversion successful! Saved to {self.nifti_output_path}")
            messagebox.showinfo("Success", f"Conversion completed! File saved to {self.nifti_output_path}")
            # 添加日志：记录保存成功
            print(f"Log - Conversion Complete: NIfTI file saved to {self.nifti_output_path}")

        except Exception as e:
            self.status_label.config(text=f"Status: Error during conversion")
            messagebox.showerror("Error", f"Conversion failed: {str(e)}")
            print(f"Error: {str(e)}")


def main():
    root = tk.Tk()
    app = DicomToNiftiConverter(root)
    root.mainloop()

if __name__ == "__main__":
    main()
