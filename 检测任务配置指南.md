# 肺结节检测任务配置指南

## 🎯 **快速开始**

### **方法一：使用自动化脚本（推荐）**

```bash
# 确保在正确的环境中
conda activate medical_imaging_env

# 运行检测任务（自动配置）
python run_detection.py \
    --image "path/to/your/ct_image.nii.gz" \
    --model "path/to/LUNA16_mD.pt"
```

### **方法二：手动配置**

如果您需要更精细的控制，可以手动配置各个文件。

## 📋 **详细配置步骤**

### **步骤1：环境准备**

```bash
# 激活医学图像处理环境
conda activate medical_imaging_env

# 验证必要的包是否已安装
python -c "import torch, monai, nibabel; print('环境检查通过')"
```

### **步骤2：下载预训练模型**

从以下链接下载预训练模型：

1. **LUNA16检测模型**：
   - 下载地址：[Zenodo - LUNA16_mD.pt](https://zenodo.org/records/14967976)
   - 文件名：`LUNA16_mD.pt`
   - 建议保存路径：`D:/lung_health_env/LUNA16_mD.pt`

2. **其他可选模型**：
   - Genesis模型：[Genesis_Chest_CT.pt](https://drive.google.com/file/d/16iIIRkl6zYAfQ14i9NOakwFd6w_xKBSY/view?usp=sharing)
   - MedicalNet3D模型：[resnet_50_23dataset.pth](https://drive.google.com/file/d/1dIyJd3jpz9mBx534UA7deqT7f8N0sbJL/view?usp=sharing)

### **步骤3：准备CT图像数据**

#### **图像格式要求**
- **格式**：NIfTI (.nii 或 .nii.gz)
- **分辨率**：建议重采样到 0.7×0.7×1.25 mm
- **强度范围**：-1000 到 500 HU
- **方向**：RAS坐标系

#### **图像预处理（如需要）**
```bash
# 单个图像预处理
python preprocess_images.py \
    --input "raw_image.nii.gz" \
    --output "processed_image.nii.gz"

# 批量预处理
python preprocess_images.py \
    --input "raw_images_dir/" \
    --output "processed_images_dir/" \
    --batch \
    --create-json "ct_detection/test_data.json"
```

### **步骤4：配置文件设置**

#### **4.1 环境配置文件** (`config/environment.json`)

```json
{
  "model_path": "D:/lung_health_env/LUNA16_mD.pt",
  "data_list_file_path": "D:/Code/AI-in-Lung-Detection/ct_detection/test_data.json",
  "data_base_dir": "D:/AIlung_test/CYJ",
  "result_list_file_path": "D:/Code/AI-in-Lung-Detection/ct_detection/output/results.json"
}
```

**参数说明：**
- `model_path`: 预训练模型文件的完整路径
- `data_list_file_path`: 测试数据列表JSON文件路径
- `data_base_dir`: 图像数据的根目录
- `result_list_file_path`: 检测结果输出文件路径

#### **4.2 测试数据配置** (`ct_detection/test_data.json`)

**单个图像：**
```json
[
    {
        "image": "D:/AIlung_test/CYJ/cyj_lps_segmented_cropped_lung.nii.gz"
    }
]
```

**多个图像：**
```json
[
    {
        "image": "path/to/image1.nii.gz"
    },
    {
        "image": "path/to/image2.nii.gz"
    },
    {
        "image": "path/to/image3.nii.gz"
    }
]
```

#### **4.3 检测参数配置** (`ct_detection/DukeLungRADS_BaseModel_epoch300_patch192x192y80z/training_config.json`)

```json
{
    "gt_box_mode": "cccwhd",
    "lr": 1e-2,
    "spacing": [0.703125, 0.703125, 1.25],
    "batch_size": 1,
    "patch_size": [192, 192, 80],
    "val_interval": 5,
    "val_batch_size": 1,
    "val_patch_size": [512, 512, 208],
    "fg_labels": [0],
    "n_input_channels": 1,
    "spatial_dims": 3,
    "score_thresh": 0.02,
    "nms_thresh": 0.22,
    "returned_layers": [1, 2],
    "conv1_t_stride": [2, 2, 1],
    "max_epoch": 300,
    "base_anchor_shapes": [[6, 8, 4], [8, 6, 5], [10, 10, 6]],
    "balanced_sampler_pos_fraction": 0.3,
    "resume_training": false,
    "resume_checkpoint_path": ""
}
```

**关键参数说明：**
- `score_thresh`: 检测置信度阈值（0.02，较低阈值检测更多候选）
- `nms_thresh`: 非极大值抑制阈值（0.22，去除重叠检测）
- `patch_size`: 训练时的图像块大小
- `val_patch_size`: 验证/测试时的图像块大小

### **步骤5：运行检测任务**

#### **方法一：使用自动化脚本**
```bash
python run_detection.py \
    --image "D:/AIlung_test/CYJ/cyj_lps_segmented_cropped_lung.nii.gz" \
    --model "D:/lung_health_env/LUNA16_mD.pt"
```

#### **方法二：手动运行**
```bash
# 进入检测目录
cd ct_detection

# 运行检测脚本
python testing.py \
    -e "../config/environment.json" \
    -c "DukeLungRADS_BaseModel_epoch300_patch192x192y80z/training_config.json"

# 返回主目录
cd ..
```

### **步骤6：查看检测结果**

检测完成后，结果将保存在 `ct_detection/output/results.json` 文件中。

**结果格式示例：**
```json
{
  "validation": [
    {
      "image": "path/to/image.nii.gz",
      "box": [
        [100, 150, 50, 120, 170, 70],
        [200, 250, 80, 220, 270, 100]
      ],
      "score": [0.85, 0.72],
      "label": [0, 0]
    }
  ]
}
```

**结果说明：**
- `box`: 边界框坐标 [x1, y1, z1, x2, y2, z2]
- `score`: 检测置信度 (0-1)
- `label`: 类别标签 (0表示结节)

## 🔧 **参数调优建议**

### **检测敏感度调整**

1. **提高敏感度（检测更多结节）**：
   - 降低 `score_thresh` (如 0.01)
   - 降低 `nms_thresh` (如 0.15)

2. **提高精确度（减少误检）**：
   - 提高 `score_thresh` (如 0.05)
   - 提高 `nms_thresh` (如 0.3)

### **性能优化**

1. **GPU内存不足**：
   - 减小 `val_patch_size` (如 [384, 384, 160])
   - 减小 `batch_size` 到 1

2. **加速推理**：
   - 使用较小的 `val_patch_size`
   - 启用混合精度推理

## 🚨 **常见问题解决**

### **问题1：模型文件找不到**
```
FileNotFoundError: [Errno 2] No such file or directory: 'model_path'
```
**解决方案：**
- 检查 `environment.json` 中的 `model_path` 是否正确
- 确认模型文件已下载并放在指定位置

### **问题2：图像文件找不到**
```
FileNotFoundError: Cannot find image file
```
**解决方案：**
- 检查 `test_data.json` 中的图像路径是否正确
- 确认图像文件存在且格式正确

### **问题3：GPU内存不足**
```
RuntimeError: CUDA out of memory
```
**解决方案：**
- 减小 `val_patch_size`
- 设置 `batch_size` 为 1
- 使用CPU推理（较慢）

### **问题4：检测结果为空**
**可能原因：**
- 置信度阈值过高
- 图像预处理不当
- 模型与数据不匹配

**解决方案：**
- 降低 `score_thresh` 到 0.01
- 检查图像预处理是否正确
- 验证模型文件完整性

## 📊 **结果分析和后续处理**

检测完成后，您可以：

1. **可视化检测结果**：
   ```bash
   python visualize_nodules_optimized.py \
       --predictions "ct_detection/output/results.json" \
       --debug
   ```

2. **进行结节分割**：
   ```bash
   python nodule_segmentation_3d_fixed.py \
       --detection-results "ct_detection/output/results.json" \
       --output-dir "output/segmentation_3d_fixed"
   ```

3. **良恶性分类**：
   ```bash
   python detect_and_classify.py
   ```

4. **影像组学特征提取**：
   ```bash
   conda activate radiomics_env
   python radiomics_feature_extractor.py \
       --input-dir "output/segmentation_3d_fixed" \
       --output-dir "output/radiomics_features"
   ```

## 📝 **总结**

通过以上配置，您可以成功运行肺结节检测任务。建议：

1. **首次使用**：使用自动化脚本 `run_detection.py`
2. **批量处理**：手动配置多图像的 `test_data.json`
3. **参数调优**：根据实际需求调整检测阈值
4. **结果验证**：使用可视化工具验证检测效果

如有问题，请检查日志输出或参考常见问题解决方案。
