#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI功能测试脚本
用于验证检测配置GUI的各项功能

主要功能：
1. 测试GUI组件创建
2. 验证配置文件读写
3. 检查依赖库安装
4. 模拟配置验证过程

Author: AI Assistant
Date: 2025-01-31
"""

import sys
import os
import json
import tempfile
from pathlib import Path

def test_dependencies():
    """测试依赖库"""
    print("=== 测试依赖库 ===")
    
    required_packages = {
        'tkinter': 'GUI界面库',
        'json': 'JSON处理库',
        'threading': '多线程库',
        'subprocess': '子进程库',
        'queue': '队列库'
    }
    
    missing_packages = []
    
    for package, desc in required_packages.items():
        try:
            __import__(package)
            print(f"✓ {package} ({desc}) - 已安装")
        except ImportError:
            print(f"✗ {package} ({desc}) - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少必要的包: {missing_packages}")
        return False
    
    print("✓ 所有依赖库检查通过")
    return True

def test_gui_creation():
    """测试GUI创建"""
    print("\n=== 测试GUI创建 ===")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 测试基本组件
        frame = ttk.Frame(root)
        label = ttk.Label(frame, text="测试标签")
        entry = ttk.Entry(frame)
        button = ttk.Button(frame, text="测试按钮")
        
        print("✓ 基本GUI组件创建成功")
        
        # 测试变量
        string_var = tk.StringVar()
        double_var = tk.DoubleVar()
        int_var = tk.IntVar()
        
        string_var.set("测试字符串")
        double_var.set(0.02)
        int_var.set(192)
        
        print("✓ GUI变量创建和设置成功")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ GUI创建失败: {str(e)}")
        return False

def test_config_file_operations():
    """测试配置文件操作"""
    print("\n=== 测试配置文件操作 ===")
    
    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 测试环境配置文件
            env_config = {
                "model_path": "test_model.pt",
                "data_list_file_path": "test_data.json",
                "data_base_dir": "test_data_dir",
                "result_list_file_path": "test_results.json"
            }
            
            env_config_path = temp_path / "environment.json"
            with open(env_config_path, 'w') as f:
                json.dump(env_config, f, indent=2)
            
            print("✓ 环境配置文件写入成功")
            
            # 读取并验证
            with open(env_config_path, 'r') as f:
                loaded_config = json.load(f)
            
            assert loaded_config == env_config
            print("✓ 环境配置文件读取验证成功")
            
            # 测试训练配置文件
            training_config = {
                "score_thresh": 0.02,
                "nms_thresh": 0.22,
                "batch_size": 1,
                "patch_size": [192, 192, 80],
                "val_patch_size": [512, 512, 208]
            }
            
            training_config_path = temp_path / "training_config.json"
            with open(training_config_path, 'w') as f:
                json.dump(training_config, f, indent=2)
            
            print("✓ 训练配置文件写入成功")
            
            # 读取并验证
            with open(training_config_path, 'r') as f:
                loaded_training_config = json.load(f)
            
            assert loaded_training_config == training_config
            print("✓ 训练配置文件读取验证成功")
            
        return True
        
    except Exception as e:
        print(f"✗ 配置文件操作失败: {str(e)}")
        return False

def test_path_validation():
    """测试路径验证功能"""
    print("\n=== 测试路径验证功能 ===")
    
    try:
        # 测试现有路径
        current_dir = os.getcwd()
        if os.path.exists(current_dir):
            print(f"✓ 当前目录存在: {current_dir}")
        
        # 测试不存在的路径
        non_existent_path = "non_existent_directory_12345"
        if not os.path.exists(non_existent_path):
            print(f"✓ 不存在路径检测正确: {non_existent_path}")
        
        # 测试文件扩展名检查
        test_files = {
            "model.pt": "PyTorch模型文件",
            "data.json": "JSON数据文件",
            "image.nii.gz": "NIfTI图像文件"
        }
        
        for filename, desc in test_files.items():
            ext = os.path.splitext(filename)[1]
            if filename.endswith('.nii.gz'):
                ext = '.nii.gz'
            print(f"✓ 文件扩展名检测: {filename} -> {ext}")
        
        return True
        
    except Exception as e:
        print(f"✗ 路径验证测试失败: {str(e)}")
        return False

def test_data_list_creation():
    """测试数据列表创建功能"""
    print("\n=== 测试数据列表创建功能 ===")
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 创建模拟的NIfTI文件
            test_files = [
                "image1.nii.gz",
                "image2.nii",
                "image3.nii.gz"
            ]
            
            for filename in test_files:
                test_file = temp_path / filename
                test_file.write_text("模拟NIfTI文件内容")
            
            print(f"✓ 创建了 {len(test_files)} 个模拟NIfTI文件")
            
            # 模拟扫描NIfTI文件
            nifti_files = list(temp_path.glob("*.nii")) + list(temp_path.glob("*.nii.gz"))
            print(f"✓ 扫描到 {len(nifti_files)} 个NIfTI文件")
            
            # 创建数据列表
            test_data = []
            for nifti_file in nifti_files:
                test_data.append({"image": str(nifti_file.absolute())})
            
            # 保存数据列表
            data_list_path = temp_path / "test_data.json"
            with open(data_list_path, 'w') as f:
                json.dump(test_data, f, indent=2)
            
            print(f"✓ 数据列表创建成功，包含 {len(test_data)} 个条目")
            
            # 验证数据列表
            with open(data_list_path, 'r') as f:
                loaded_data = json.load(f)
            
            assert len(loaded_data) == len(test_data)
            print("✓ 数据列表验证成功")
            
        return True
        
    except Exception as e:
        print(f"✗ 数据列表创建测试失败: {str(e)}")
        return False

def test_gui_import():
    """测试GUI模块导入"""
    print("\n=== 测试GUI模块导入 ===")
    
    try:
        # 测试导入GUI模块
        sys.path.insert(0, os.getcwd())
        
        # 检查GUI文件是否存在
        gui_file = "detection_config_gui.py"
        if not os.path.exists(gui_file):
            print(f"✗ GUI文件不存在: {gui_file}")
            return False
        
        print(f"✓ GUI文件存在: {gui_file}")
        
        # 尝试导入（不运行）
        import importlib.util
        spec = importlib.util.spec_from_file_location("detection_config_gui", gui_file)
        if spec is None:
            print("✗ 无法创建模块规范")
            return False
        
        print("✓ GUI模块导入规范创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI模块导入测试失败: {str(e)}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("GUI功能测试脚本")
    print("=" * 50)
    
    tests = [
        ("依赖库测试", test_dependencies),
        ("GUI创建测试", test_gui_creation),
        ("配置文件操作测试", test_config_file_operations),
        ("路径验证测试", test_path_validation),
        ("数据列表创建测试", test_data_list_creation),
        ("GUI模块导入测试", test_gui_import)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("✓ 所有测试通过！GUI应该可以正常运行。")
        return True
    else:
        print("✗ 部分测试失败，请检查环境配置。")
        return False

def main():
    """主函数"""
    success = run_all_tests()
    
    if success:
        print("\n建议:")
        print("1. 运行 'python detection_config_gui.py' 启动GUI")
        print("2. 或双击 '启动检测配置GUI.bat' 文件")
    else:
        print("\n建议:")
        print("1. 检查conda环境是否正确激活")
        print("2. 安装缺少的依赖包")
        print("3. 确保在正确的项目目录中运行")
    
    return success

if __name__ == "__main__":
    main()
