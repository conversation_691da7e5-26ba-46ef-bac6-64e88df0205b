# Copyright (c) MONAI Consortium
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import sys
import os

current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(current_dir, "..", "ct_classification"))

import argparse
import json
import logging
import sys
import time
from copy import deepcopy

import numpy as np
import torch
from ct_classification.generate_transforms import generate_detection_inference_transform

import monai
from monai.apps.detection.networks.retinanet_detector import RetinaNetDetector
from monai.apps.detection.networks.retinanet_network import (
    RetinaNet,
    resnet_fpn_feature_extractor,
)
from monai.apps.detection.transforms.dictionary import ClipBoxToImaged
from monai.apps.detection.utils.anchor_utils import Anchor<PERSON><PERSON>atorWithAnchorShape
from monai.data import DataLoader, Dataset, load_decathlon_datalist
from monai.data.utils import no_collation
from monai.networks.nets import resnet
from monai.transforms import Compose, DeleteItemsd, Invertd, ScaleIntensityRanged, EnsureChannelFirstd, Orientationd, EnsureTyped
from monai.transforms import InvertibleTransform


def print_coordinate_info(image, affine=None, name=""):
    """
    打印图像和坐标系信息
    """
    print(f"\n===== {name} 坐标信息 =====")
    print(f"图像形状: {image.shape}")
    print(f"图像数据类型: {image.dtype}")
    print(f"图像值范围: [{np.min(image)}, {np.max(image)}]")
    
    # 打印图像中心点的值
    center_x, center_y, center_z = [s // 2 for s in image.shape]
    print(f"图像中心点 ({center_x}, {center_y}, {center_z}) 的值: {image[center_x, center_y, center_z]}")
    
    # 如果有仿射矩阵，打印相关信息
    if affine is not None:
        print(f"仿射矩阵:\n{affine}")
        print(f"仿射矩阵行列式: {np.linalg.det(affine[:3, :3])}")
        
        # 计算从体素坐标到世界坐标的转换
        voxel_coords = np.array([center_x, center_y, center_z, 1])
        world_coords = np.dot(affine, voxel_coords)
        print(f"中心体素坐标 {voxel_coords[:3]} 对应的世界坐标: {world_coords[:3]}")
        
        # 计算从世界坐标到体素坐标的转换
        inv_affine = np.linalg.inv(affine)
        voxel_coords_back = np.dot(inv_affine, world_coords)
        print(f"世界坐标 {world_coords[:3]} 转回体素坐标: {voxel_coords_back[:3]}")
    
    print("=" * 30)


def print_box_info(boxes, scores=None, name=""):
    """
    打印边界框信息
    """
    print(f"\n===== {name} 边界框信息 =====")
    print(f"边界框数量: {len(boxes)}")
    
    for i, box in enumerate(boxes[:5]):  # 只打印前5个边界框
        box_info = f"框 #{i+1}: [{box[0]:.1f}, {box[1]:.1f}, {box[2]:.1f}, {box[3]:.1f}, {box[4]:.1f}, {box[5]:.1f}]"
        if scores is not None and i < len(scores):
            box_info += f", 置信度: {scores[i]:.4f}"
        print(box_info)
        
        # 计算框的尺寸
        width = box[3] - box[0]
        height = box[4] - box[1]
        depth = box[5] - box[2]
        print(f"  尺寸: 宽={width:.1f}, 高={height:.1f}, 深={depth:.1f}")
        
        # 计算框的中心点
        center_x = (box[0] + box[3]) / 2
        center_y = (box[1] + box[4]) / 2
        center_z = (box[2] + box[5]) / 2
        print(f"  中心点: ({center_x:.1f}, {center_y:.1f}, {center_z:.1f})")
    
    if len(boxes) > 5:
        print(f"... 还有 {len(boxes) - 5} 个边界框未显示")
    print("=" * 30)


def check_transform_invertibility(transform):
    """
    检查变换的可逆性和元数据跟踪情况
    
    Args:
        transform: 要检查的变换组合
    
    Returns:
        dict: 包含可逆性检查结果的字典
    """
    result = {
        "invertible_transforms": [],
        "non_invertible_transforms": [],
        "has_map_items": False
    }
    
    # 检查整体变换是否设置了map_items
    if hasattr(transform, "map_items"):
        result["has_map_items"] = transform.map_items
    
    # 递归检查所有子变换
    if hasattr(transform, "transforms"):
        for t in transform.transforms:
            # 检查变换是否可逆
            if isinstance(t, InvertibleTransform):
                result["invertible_transforms"].append(t.__class__.__name__)
            else:
                result["non_invertible_transforms"].append(t.__class__.__name__)
            
            # 递归检查嵌套变换
            if hasattr(t, "transforms"):
                nested_result = check_transform_invertibility(t)
                result["invertible_transforms"].extend(nested_result["invertible_transforms"])
                result["non_invertible_transforms"].extend(nested_result["non_invertible_transforms"])
    
    return result


def main():
    parser = argparse.ArgumentParser(description="PyTorch Object Detection Testing")
    parser.add_argument(
        "-e",
        "--environment-file",
        default="../config/environment.json",
        help="environment json file that stores environment path",
    )
    parser.add_argument(
        "-c",
        "--config-file",
        default="../config/config_test.json",
        help="config json file that stores hyper-parameters",
    )
    args = parser.parse_args()

    amp = True

    monai.config.print_config()

    env_dict = json.load(open(args.environment_file, "r"))
    config_dict = json.load(open(args.config_file, "r"))

    for k, v in env_dict.items():
        setattr(args, k, v)
    for k, v in config_dict.items():
        setattr(args, k, v)

    patch_size = args.val_patch_size

    # 1. define transform
    intensity_transform = ScaleIntensityRanged(
        keys=["image"],
        a_min=-1024,
        a_max=300.0,
        b_min=0.0,
        b_max=1.0,
        clip=True,
    )
    inference_transforms, post_transforms = generate_detection_inference_transform(
        "image",
        "pred_box",
        "pred_label",
        "pred_score",
        args.gt_box_mode,
        intensity_transform,
        affine_lps_to_ras=True,
        amp=amp,
    )
    
    # 添加调试信息，验证变换配置
    print("\n===== 变换配置信息 =====")
    print(f"使用的图像键: image")
    print(f"预测框键: pred_box")
    print(f"gt_box_mode: {args.gt_box_mode}")
    print(f"affine_lps_to_ras: True")
    print(f"是否使用AMP: {amp}")
    
    # 检查变换组件
    print("\n变换链中的组件:")
    for i, transform in enumerate(inference_transforms.transforms):
        print(f"  {i+1}. {transform.__class__.__name__}")
    
    print("\n后处理变换链中的组件:")
    for i, transform in enumerate(post_transforms.transforms):
        print(f"  {i+1}. {transform.__class__.__name__}")
    
    # 检查变换的可逆性
    print("\n检查变换的可逆性:")
    infer_check = check_transform_invertibility(inference_transforms)
    post_check = check_transform_invertibility(post_transforms)
    
    print("推理变换:")
    print(f"  设置了map_items: {infer_check['has_map_items']}")
    print(f"  可逆变换: {infer_check['invertible_transforms']}")
    print(f"  不可逆变换: {infer_check['non_invertible_transforms']}")
    
    print("后处理变换:")
    print(f"  设置了map_items: {post_check['has_map_items']}")
    print(f"  可逆变换: {post_check['invertible_transforms']}")
    print(f"  不可逆变换: {post_check['non_invertible_transforms']}")
    print("=" * 30)

    # 2. create a inference data loader
    inference_data = load_decathlon_datalist(
        args.data_list_file_path,
        is_segmentation=True,
        data_list_key="validation",
        base_dir=args.data_base_dir,
    )
    inference_ds = Dataset(
        data=inference_data,
        transform=inference_transforms,
    )
    inference_loader = DataLoader(
        inference_ds,
        batch_size=1,
        num_workers=4,
        pin_memory=torch.cuda.is_available(),
        collate_fn=no_collation,
    )

    # 3. build model
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 1) build anchor generator
    # returned_layers: when target boxes are small, set it smaller
    # base_anchor_shapes: anchor shape for the most high-resolution output,
    #   when target boxes are small, set it smaller
    anchor_generator = AnchorGeneratorWithAnchorShape(
        feature_map_scales=[2**l for l in range(len(args.returned_layers) + 1)],
        base_anchor_shapes=args.base_anchor_shapes,
    )

    # 2) build network
    net = torch.jit.load(env_dict["model_path"]).to(device)
    print(f"Load model from {env_dict['model_path']}")

    # 3) build detector
    detector = RetinaNetDetector(
        network=net, anchor_generator=anchor_generator, debug=False
    )

    # set inference components
    detector.set_box_selector_parameters(
        score_thresh=args.score_thresh,
        topk_candidates_per_level=1000,
        nms_thresh=args.nms_thresh,
        detections_per_img=100,
    )
    detector.set_sliding_window_inferer(
        roi_size=patch_size,
        overlap=0.25,
        sw_batch_size=1,
        mode="gaussian",
        device="cpu",
    )

    # 4. apply trained model
    results_dict = {"validation": []}
    detector.eval()

    with torch.no_grad():
        start_time = time.time()
        for inference_data in inference_loader:
            print("\n========== 处理新的图像 ==========")
            print("Data keys:", inference_data[0].keys())
            print("Meta dict keys:", [k for k in inference_data[0].keys() if "_meta_dict" in k])
            
            # 在应用 post_transforms 之前提取文件名
            inference_img_filenames = []
            for inference_data_i in inference_data:
                if hasattr(inference_data_i["image"], "meta") and "filename_or_obj" in inference_data_i["image"].meta:
                    filename = inference_data_i["image"].meta["filename_or_obj"]
                else:
                    filename = "unknown"
                    print("Warning: 'filename_or_obj' not found in image meta attribute.")
                inference_img_filenames.append(filename)
            print("Inference image filenames:", inference_img_filenames)
            
            # 打印输入图像信息
            for i, inference_data_i in enumerate(inference_data):
                img_tensor = inference_data_i["image"]
                img_np = img_tensor.cpu().numpy()
                print_coordinate_info(img_np[0], name=f"输入图像 {i}")
                
                # 如果有元数据中的仿射矩阵，打印它
                if "image_meta_dict" in inference_data_i and "affine" in inference_data_i["image_meta_dict"]:
                    affine = inference_data_i["image_meta_dict"]["affine"]
                    print(f"元数据中的仿射矩阵:\n{affine}")
                
            use_inferer = not all(
                [
                    inference_data_i["image"][0, ...].numel() < np.prod(patch_size)
                    for inference_data_i in inference_data
                ]
            )
            inference_inputs = [
                inference_data_i["image"].to(device)
                for inference_data_i in inference_data
            ]

            if amp:
                with torch.amp.autocast('cuda'):
                    inference_outputs = detector(
                        inference_inputs, use_inferer=use_inferer
                    )
            else:
                inference_outputs = detector(inference_inputs, use_inferer=use_inferer)
            del inference_inputs

            # 打印原始预测框信息
            for i, inference_output in enumerate(inference_outputs):
                print(f"\n----- 图像 {i} 的原始预测结果 -----")
                if detector.target_box_key in inference_output:
                    boxes = inference_output[detector.target_box_key].cpu().numpy()
                    scores = inference_output[detector.pred_score_key].cpu().numpy()
                    print_box_info(boxes, scores, name=f"原始预测框")

            # update inference_data for post transform
            for i in range(len(inference_outputs)):
                inference_data_i, inference_pred_i = (
                    inference_data[i],
                    inference_outputs[i],
                )
                
                # 添加调试信息 - 输出变换前的数据情况
                print(f"\n----- 应用后处理变换前的数据状态 (图像 {i}) -----")
                if hasattr(inference_data_i["image"], "meta"):
                    print(f"图像元数据可用: {list(inference_data_i['image'].meta.keys())}")
                else:
                    print("警告: 图像元数据不可用")
                    
                if "image_meta_dict" in inference_data_i:
                    print(f"图像元数据字典可用: {list(inference_data_i['image_meta_dict'].keys())}")
                else:
                    print("警告: 图像元数据字典不可用")
                
                inference_data_i["pred_box"] = inference_pred_i[
                    detector.target_box_key
                ].to(torch.float32)
                inference_data_i["pred_label"] = inference_pred_i[
                    detector.target_label_key
                ]
                inference_data_i["pred_score"] = inference_pred_i[
                    detector.pred_score_key
                ].to(torch.float32)
                
                # 应用后处理变换并添加调试信息
                print(f"\n应用后处理变换...")
                inference_data[i] = post_transforms(inference_data_i)
                
                # 添加调试信息 - 输出变换后的数据情况
                print(f"\n----- 应用后处理变换后的数据状态 (图像 {i}) -----")
                if "image_meta_dict" in inference_data[i]:
                    print(f"变换后图像元数据字典可用: {list(inference_data[i]['image_meta_dict'].keys())}")
                else:
                    print("警告: 变换后图像元数据字典不可用")
                    
                if "pred_box" in inference_data[i]:
                    boxes = inference_data[i]["pred_box"]
                    print(f"变换后边界框形状: {boxes.shape}")
                else:
                    print("警告: 变换后边界框不可用")

            # 打印反转变换后的预测框信息
            for i, inference_data_i in enumerate(inference_data):
                print(f"\n----- 图像 {i} 的反转变换后预测结果 -----")
                if "pred_box" in inference_data_i:
                    boxes = inference_data_i["pred_box"].cpu().numpy()
                    scores = inference_data_i["pred_score"].cpu().numpy()
                    print_box_info(boxes, scores, name=f"反转变换后预测框")

            for inference_img_filename, inference_pred_i in zip(
                inference_img_filenames, inference_data
            ):
                result = {
                    "label": inference_pred_i["pred_label"]
                    .cpu()
                    .detach()
                    .numpy()
                    .tolist(),
                    "box": inference_pred_i["pred_box"].cpu().detach().numpy().tolist(),
                    "score": inference_pred_i["pred_score"]
                    .cpu()
                    .detach()
                    .numpy()
                    .tolist(),
                }
                result.update({"image": inference_img_filename})
                results_dict["validation"].append(result)
                
                # 保存坐标系信息到结果中，方便后续分析
                if "image_meta_dict" in inference_pred_i and "affine" in inference_pred_i["image_meta_dict"]:
                    result["affine"] = inference_pred_i["image_meta_dict"]["affine"].tolist()
                if "image_meta_dict" in inference_pred_i and "spatial_shape" in inference_pred_i["image_meta_dict"]:
                    result["spatial_shape"] = inference_pred_i["image_meta_dict"]["spatial_shape"].tolist()
                if "image_meta_dict" in inference_pred_i and "original_affine" in inference_pred_i["image_meta_dict"]:
                    result["original_affine"] = inference_pred_i["image_meta_dict"]["original_affine"].tolist()

    end_time = time.time()
    print("Testing time: ", end_time - start_time)

    # 添加坐标系信息到结果文件
    results_dict["coordinate_system_info"] = {
        "description": "包含坐标变换信息，用于调试边界框问题",
        "affine_lps_to_ras": True,  # 根据generate_detection_inference_transform的参数
        "gt_box_mode": args.gt_box_mode
    }

    with open(args.result_list_file_path, "w") as outfile:
        json.dump(results_dict, outfile, indent=4)


if __name__ == "__main__":
    logging.basicConfig(
        stream=sys.stdout,
        level=logging.INFO,
        format="[%(asctime)s.%(msecs)03d][%(levelname)5s](%(name)s) - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    main()
