#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
肺结节检测任务配置GUI界面
提供直观的图形界面来配置检测任务参数

主要功能：
1. 可视化配置检测参数
2. 文件路径选择器
3. 参数验证和保存
4. 一键运行检测任务
5. 实时日志显示

Author: AI Assistant
Date: 2025-01-31
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import os
import sys
import threading
import subprocess
from pathlib import Path
import queue

class DetectionConfigGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("肺结节检测任务配置界面")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 配置变量
        self.config_vars = {
            'model_path': tk.StringVar(),
            'data_list_file_path': tk.StringVar(),
            'data_base_dir': tk.StringVar(),
            'result_list_file_path': tk.StringVar(),
            'score_thresh': tk.DoubleVar(value=0.02),
            'nms_thresh': tk.DoubleVar(value=0.22),
            'batch_size': tk.IntVar(value=1),
            'patch_size_x': tk.IntVar(value=192),
            'patch_size_y': tk.IntVar(value=192),
            'patch_size_z': tk.IntVar(value=80),
            'val_patch_size_x': tk.IntVar(value=512),
            'val_patch_size_y': tk.IntVar(value=512),
            'val_patch_size_z': tk.IntVar(value=208)
        }
        
        # 日志队列
        self.log_queue = queue.Queue()
        
        self.create_widgets()
        self.load_existing_config()
        
    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="肺结节检测任务配置", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件路径配置区域
        self.create_file_config_section(main_frame, 1)
        
        # 检测参数配置区域
        self.create_detection_params_section(main_frame, 2)
        
        # 按钮区域
        self.create_button_section(main_frame, 3)
        
        # 日志显示区域
        self.create_log_section(main_frame, 4)
        
    def create_file_config_section(self, parent, row):
        """创建文件配置区域"""
        # 文件配置框架
        file_frame = ttk.LabelFrame(parent, text="文件路径配置", padding="10")
        file_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # 预训练模型路径
        ttk.Label(file_frame, text="预训练模型:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(file_frame, textvariable=self.config_vars['model_path'], width=50).grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(file_frame, text="浏览", 
                  command=lambda: self.browse_file('model_path', 'PyTorch模型文件', '*.pt')).grid(
            row=0, column=2, padx=(5, 0), pady=2)
        
        # 测试数据列表文件
        ttk.Label(file_frame, text="测试数据列表:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(file_frame, textvariable=self.config_vars['data_list_file_path'], width=50).grid(
            row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(file_frame, text="浏览", 
                  command=lambda: self.browse_file('data_list_file_path', 'JSON文件', '*.json')).grid(
            row=1, column=2, padx=(5, 0), pady=2)
        
        # 图像数据根目录
        ttk.Label(file_frame, text="图像数据目录:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(file_frame, textvariable=self.config_vars['data_base_dir'], width=50).grid(
            row=2, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(file_frame, text="浏览", 
                  command=lambda: self.browse_directory('data_base_dir')).grid(
            row=2, column=2, padx=(5, 0), pady=2)
        
        # 检测结果输出路径
        ttk.Label(file_frame, text="结果输出路径:").grid(row=3, column=0, sticky=tk.W, pady=2)
        ttk.Entry(file_frame, textvariable=self.config_vars['result_list_file_path'], width=50).grid(
            row=3, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(file_frame, text="浏览", 
                  command=lambda: self.browse_save_file('result_list_file_path', 'JSON文件', '*.json')).grid(
            row=3, column=2, padx=(5, 0), pady=2)
        
        # 快速设置按钮
        quick_setup_frame = ttk.Frame(file_frame)
        quick_setup_frame.grid(row=4, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(quick_setup_frame, text="创建测试数据列表", 
                  command=self.create_test_data_list).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_setup_frame, text="设置默认输出路径", 
                  command=self.set_default_output_path).pack(side=tk.LEFT, padx=5)
        
    def create_detection_params_section(self, parent, row):
        """创建检测参数配置区域"""
        # 检测参数框架
        params_frame = ttk.LabelFrame(parent, text="检测参数配置", padding="10")
        params_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 左侧参数
        left_frame = ttk.Frame(params_frame)
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.N), padx=(0, 20))
        
        ttk.Label(left_frame, text="置信度阈值:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(left_frame, textvariable=self.config_vars['score_thresh'], width=10).grid(
            row=0, column=1, padx=(5, 0), pady=2)
        
        ttk.Label(left_frame, text="NMS阈值:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(left_frame, textvariable=self.config_vars['nms_thresh'], width=10).grid(
            row=1, column=1, padx=(5, 0), pady=2)
        
        ttk.Label(left_frame, text="批处理大小:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(left_frame, textvariable=self.config_vars['batch_size'], width=10).grid(
            row=2, column=1, padx=(5, 0), pady=2)
        
        # 右侧参数
        right_frame = ttk.Frame(params_frame)
        right_frame.grid(row=0, column=1, sticky=(tk.W, tk.N))
        
        ttk.Label(right_frame, text="训练块大小 (X×Y×Z):").grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=2)
        patch_frame = ttk.Frame(right_frame)
        patch_frame.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=2)
        
        ttk.Entry(patch_frame, textvariable=self.config_vars['patch_size_x'], width=8).pack(side=tk.LEFT)
        ttk.Label(patch_frame, text="×").pack(side=tk.LEFT, padx=2)
        ttk.Entry(patch_frame, textvariable=self.config_vars['patch_size_y'], width=8).pack(side=tk.LEFT)
        ttk.Label(patch_frame, text="×").pack(side=tk.LEFT, padx=2)
        ttk.Entry(patch_frame, textvariable=self.config_vars['patch_size_z'], width=8).pack(side=tk.LEFT)
        
        ttk.Label(right_frame, text="验证块大小 (X×Y×Z):").grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=(10, 2))
        val_patch_frame = ttk.Frame(right_frame)
        val_patch_frame.grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=2)
        
        ttk.Entry(val_patch_frame, textvariable=self.config_vars['val_patch_size_x'], width=8).pack(side=tk.LEFT)
        ttk.Label(val_patch_frame, text="×").pack(side=tk.LEFT, padx=2)
        ttk.Entry(val_patch_frame, textvariable=self.config_vars['val_patch_size_y'], width=8).pack(side=tk.LEFT)
        ttk.Label(val_patch_frame, text="×").pack(side=tk.LEFT, padx=2)
        ttk.Entry(val_patch_frame, textvariable=self.config_vars['val_patch_size_z'], width=8).pack(side=tk.LEFT)
        
        # 参数说明
        info_frame = ttk.Frame(params_frame)
        info_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        info_text = ("参数说明:\n"
                    "• 置信度阈值: 越低检测越敏感，越高精确度越高 (推荐: 0.02)\n"
                    "• NMS阈值: 控制重叠检测的去除程度 (推荐: 0.22)\n"
                    "• 验证块大小: 越大内存占用越多，但可能提高精度")
        
        ttk.Label(info_frame, text=info_text, font=('Arial', 9), foreground='gray').pack(anchor=tk.W)
        
    def create_button_section(self, parent, row):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=row, column=0, columnspan=3, pady=(0, 10))
        
        ttk.Button(button_frame, text="验证配置", command=self.validate_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="加载配置", command=self.load_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="运行检测", command=self.run_detection, 
                  style='Accent.TButton').pack(side=tk.LEFT, padx=(10, 0))
        
    def create_log_section(self, parent, row):
        """创建日志显示区域"""
        log_frame = ttk.LabelFrame(parent, text="运行日志", padding="5")
        log_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 配置主框架的行权重
        parent.rowconfigure(row, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 日志控制按钮
        log_button_frame = ttk.Frame(log_frame)
        log_button_frame.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        
        ttk.Button(log_button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT)
        ttk.Button(log_button_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=(5, 0))
        
    def browse_file(self, var_name, file_desc, file_types):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title=f"选择{file_desc}",
            filetypes=[(file_desc, file_types), ("所有文件", "*.*")]
        )
        if filename:
            self.config_vars[var_name].set(filename)
            self.log(f"已选择{file_desc}: {filename}")
    
    def browse_directory(self, var_name):
        """浏览目录"""
        dirname = filedialog.askdirectory(title="选择目录")
        if dirname:
            self.config_vars[var_name].set(dirname)
            self.log(f"已选择目录: {dirname}")
    
    def browse_save_file(self, var_name, file_desc, file_types):
        """浏览保存文件"""
        filename = filedialog.asksaveasfilename(
            title=f"保存{file_desc}",
            filetypes=[(file_desc, file_types), ("所有文件", "*.*")],
            defaultextension=".json"
        )
        if filename:
            self.config_vars[var_name].set(filename)
            self.log(f"已设置输出路径: {filename}")
    
    def create_test_data_list(self):
        """创建测试数据列表"""
        data_dir = self.config_vars['data_base_dir'].get()
        if not data_dir:
            messagebox.showwarning("警告", "请先选择图像数据目录")
            return
        
        # 查找NIfTI文件
        data_path = Path(data_dir)
        nifti_files = list(data_path.glob("*.nii")) + list(data_path.glob("*.nii.gz"))
        
        if not nifti_files:
            messagebox.showwarning("警告", "在指定目录中未找到NIfTI文件")
            return
        
        # 创建测试数据列表
        test_data = []
        for nifti_file in nifti_files:
            test_data.append({"image": str(nifti_file.absolute())})
        
        # 保存文件
        save_path = filedialog.asksaveasfilename(
            title="保存测试数据列表",
            filetypes=[("JSON文件", "*.json")],
            defaultextension=".json",
            initialname="test_data.json"
        )
        
        if save_path:
            with open(save_path, 'w') as f:
                json.dump(test_data, f, indent=2)
            
            self.config_vars['data_list_file_path'].set(save_path)
            self.log(f"已创建测试数据列表: {save_path}")
            self.log(f"包含 {len(test_data)} 个图像文件")
    
    def set_default_output_path(self):
        """设置默认输出路径"""
        default_path = "ct_detection/output/results.json"
        os.makedirs(os.path.dirname(default_path), exist_ok=True)
        self.config_vars['result_list_file_path'].set(os.path.abspath(default_path))
        self.log(f"已设置默认输出路径: {os.path.abspath(default_path)}")
    
    def log(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def save_log(self):
        """保存日志"""
        log_content = self.log_text.get(1.0, tk.END)
        filename = filedialog.asksaveasfilename(
            title="保存日志",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            defaultextension=".txt"
        )
        if filename:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(log_content)
            self.log(f"日志已保存至: {filename}")
    
    def validate_config(self):
        """验证配置"""
        self.log("开始验证配置...")
        
        # 检查必需的文件路径
        required_paths = {
            'model_path': '预训练模型文件',
            'data_list_file_path': '测试数据列表文件',
            'data_base_dir': '图像数据目录',
            'result_list_file_path': '结果输出路径'
        }
        
        for var_name, desc in required_paths.items():
            path = self.config_vars[var_name].get()
            if not path:
                self.log(f"✗ 错误: {desc}未设置")
                messagebox.showerror("配置错误", f"{desc}未设置")
                return False
        
        # 检查文件是否存在
        model_path = self.config_vars['model_path'].get()
        if not os.path.exists(model_path):
            self.log(f"✗ 错误: 模型文件不存在: {model_path}")
            messagebox.showerror("配置错误", f"模型文件不存在: {model_path}")
            return False
        
        data_list_path = self.config_vars['data_list_file_path'].get()
        if not os.path.exists(data_list_path):
            self.log(f"✗ 错误: 数据列表文件不存在: {data_list_path}")
            messagebox.showerror("配置错误", f"数据列表文件不存在: {data_list_path}")
            return False
        
        data_base_dir = self.config_vars['data_base_dir'].get()
        if not os.path.exists(data_base_dir):
            self.log(f"✗ 错误: 数据目录不存在: {data_base_dir}")
            messagebox.showerror("配置错误", f"数据目录不存在: {data_base_dir}")
            return False
        
        # 验证数据列表文件内容
        try:
            with open(data_list_path, 'r') as f:
                data_list = json.load(f)
            
            if not isinstance(data_list, list) or not data_list:
                self.log("✗ 错误: 数据列表文件格式不正确或为空")
                messagebox.showerror("配置错误", "数据列表文件格式不正确或为空")
                return False
            
            # 检查图像文件是否存在
            for item in data_list:
                image_path = item.get('image')
                if not image_path or not os.path.exists(image_path):
                    self.log(f"✗ 错误: 图像文件不存在: {image_path}")
                    messagebox.showerror("配置错误", f"图像文件不存在: {image_path}")
                    return False
            
            self.log(f"✓ 数据列表验证通过，包含 {len(data_list)} 个图像")
            
        except Exception as e:
            self.log(f"✗ 错误: 读取数据列表文件失败: {str(e)}")
            messagebox.showerror("配置错误", f"读取数据列表文件失败: {str(e)}")
            return False
        
        # 创建输出目录
        result_path = self.config_vars['result_list_file_path'].get()
        os.makedirs(os.path.dirname(result_path), exist_ok=True)
        
        self.log("✓ 配置验证通过")
        messagebox.showinfo("验证成功", "所有配置项验证通过！")
        return True

    def save_config(self):
        """保存配置到文件"""
        if not self.validate_config():
            return

        # 构建环境配置
        env_config = {
            "model_path": self.config_vars['model_path'].get(),
            "data_list_file_path": self.config_vars['data_list_file_path'].get(),
            "data_base_dir": self.config_vars['data_base_dir'].get(),
            "result_list_file_path": self.config_vars['result_list_file_path'].get()
        }

        # 构建训练配置
        training_config = {
            "gt_box_mode": "cccwhd",
            "lr": 1e-2,
            "spacing": [0.703125, 0.703125, 1.25],
            "batch_size": self.config_vars['batch_size'].get(),
            "patch_size": [
                self.config_vars['patch_size_x'].get(),
                self.config_vars['patch_size_y'].get(),
                self.config_vars['patch_size_z'].get()
            ],
            "val_interval": 5,
            "val_batch_size": 1,
            "val_patch_size": [
                self.config_vars['val_patch_size_x'].get(),
                self.config_vars['val_patch_size_y'].get(),
                self.config_vars['val_patch_size_z'].get()
            ],
            "fg_labels": [0],
            "n_input_channels": 1,
            "spatial_dims": 3,
            "score_thresh": self.config_vars['score_thresh'].get(),
            "nms_thresh": self.config_vars['nms_thresh'].get(),
            "returned_layers": [1, 2],
            "conv1_t_stride": [2, 2, 1],
            "max_epoch": 300,
            "base_anchor_shapes": [[6, 8, 4], [8, 6, 5], [10, 10, 6]],
            "balanced_sampler_pos_fraction": 0.3,
            "resume_training": False,
            "resume_checkpoint_path": ""
        }

        try:
            # 保存环境配置
            os.makedirs("config", exist_ok=True)
            env_config_path = "config/environment.json"
            with open(env_config_path, 'w') as f:
                json.dump(env_config, f, indent=2)

            # 保存训练配置
            training_config_dir = "ct_detection/DukeLungRADS_BaseModel_epoch300_patch192x192y80z"
            os.makedirs(training_config_dir, exist_ok=True)
            training_config_path = os.path.join(training_config_dir, "training_config.json")
            with open(training_config_path, 'w') as f:
                json.dump(training_config, f, indent=2)

            self.log(f"✓ 环境配置已保存: {env_config_path}")
            self.log(f"✓ 训练配置已保存: {training_config_path}")
            messagebox.showinfo("保存成功", "配置文件已保存！")

        except Exception as e:
            self.log(f"✗ 保存配置失败: {str(e)}")
            messagebox.showerror("保存失败", f"保存配置失败: {str(e)}")

    def load_config(self):
        """从文件加载配置"""
        env_config_path = "config/environment.json"
        training_config_path = "ct_detection/DukeLungRADS_BaseModel_epoch300_patch192x192y80z/training_config.json"

        try:
            # 加载环境配置
            if os.path.exists(env_config_path):
                with open(env_config_path, 'r') as f:
                    env_config = json.load(f)

                self.config_vars['model_path'].set(env_config.get('model_path', ''))
                self.config_vars['data_list_file_path'].set(env_config.get('data_list_file_path', ''))
                self.config_vars['data_base_dir'].set(env_config.get('data_base_dir', ''))
                self.config_vars['result_list_file_path'].set(env_config.get('result_list_file_path', ''))

                self.log(f"✓ 已加载环境配置: {env_config_path}")

            # 加载训练配置
            if os.path.exists(training_config_path):
                with open(training_config_path, 'r') as f:
                    training_config = json.load(f)

                self.config_vars['score_thresh'].set(training_config.get('score_thresh', 0.02))
                self.config_vars['nms_thresh'].set(training_config.get('nms_thresh', 0.22))
                self.config_vars['batch_size'].set(training_config.get('batch_size', 1))

                patch_size = training_config.get('patch_size', [192, 192, 80])
                self.config_vars['patch_size_x'].set(patch_size[0])
                self.config_vars['patch_size_y'].set(patch_size[1])
                self.config_vars['patch_size_z'].set(patch_size[2])

                val_patch_size = training_config.get('val_patch_size', [512, 512, 208])
                self.config_vars['val_patch_size_x'].set(val_patch_size[0])
                self.config_vars['val_patch_size_y'].set(val_patch_size[1])
                self.config_vars['val_patch_size_z'].set(val_patch_size[2])

                self.log(f"✓ 已加载训练配置: {training_config_path}")

            messagebox.showinfo("加载成功", "配置文件已加载！")

        except Exception as e:
            self.log(f"✗ 加载配置失败: {str(e)}")
            messagebox.showerror("加载失败", f"加载配置失败: {str(e)}")

    def load_existing_config(self):
        """启动时自动加载现有配置"""
        self.load_config()

    def run_detection(self):
        """运行检测任务"""
        if not self.validate_config():
            return

        # 先保存配置
        self.save_config()

        # 确认运行
        if not messagebox.askyesno("确认运行", "是否开始运行检测任务？\n这可能需要较长时间。"):
            return

        self.log("开始运行检测任务...")

        # 在新线程中运行检测任务
        detection_thread = threading.Thread(target=self._run_detection_task)
        detection_thread.daemon = True
        detection_thread.start()

    def _run_detection_task(self):
        """在后台线程中运行检测任务"""
        try:
            env_config_path = "config/environment.json"
            training_config_path = "ct_detection/DukeLungRADS_BaseModel_epoch300_patch192x192y80z/training_config.json"

            # 构建命令
            cmd = [
                sys.executable,
                "ct_detection/testing.py",
                "-e", env_config_path,
                "-c", training_config_path
            ]

            self.log(f"执行命令: {' '.join(cmd)}")

            # 运行检测
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # 实时读取输出
            for line in process.stdout:
                self.root.after(0, lambda l=line: self.log(l.strip()))

            process.wait()

            if process.returncode == 0:
                self.root.after(0, lambda: self.log("✓ 检测任务完成"))
                self.root.after(0, lambda: messagebox.showinfo("任务完成", "检测任务已成功完成！"))

                # 询问是否查看结果
                result_path = self.config_vars['result_list_file_path'].get()
                if os.path.exists(result_path):
                    self.root.after(0, lambda: self._show_results(result_path))
            else:
                self.root.after(0, lambda: self.log("✗ 检测任务失败"))
                self.root.after(0, lambda: messagebox.showerror("任务失败", "检测任务执行失败，请查看日志"))

        except Exception as e:
            self.root.after(0, lambda: self.log(f"✗ 运行检测任务时出错: {str(e)}"))
            self.root.after(0, lambda: messagebox.showerror("运行错误", f"运行检测任务时出错: {str(e)}"))

    def _show_results(self, result_path):
        """显示检测结果摘要"""
        try:
            with open(result_path, 'r') as f:
                results = json.load(f)

            if 'validation' in results:
                validation_results = results['validation']
                total_images = len(validation_results)
                total_detections = sum(len(result.get('box', [])) for result in validation_results)

                summary = f"检测结果摘要:\n"
                summary += f"处理图像数量: {total_images}\n"
                summary += f"检测结节总数: {total_detections}\n\n"

                for i, result in enumerate(validation_results):
                    image_name = os.path.basename(result.get('image', 'Unknown'))
                    num_detections = len(result.get('box', []))
                    scores = result.get('score', [])

                    summary += f"图像 {i+1}: {image_name}\n"
                    summary += f"  检测数量: {num_detections}\n"

                    if scores:
                        max_score = max(scores)
                        avg_score = sum(scores) / len(scores)
                        summary += f"  最高置信度: {max_score:.3f}\n"
                        summary += f"  平均置信度: {avg_score:.3f}\n"

                    summary += "\n"

                # 显示结果窗口
                result_window = tk.Toplevel(self.root)
                result_window.title("检测结果")
                result_window.geometry("500x400")

                result_text = scrolledtext.ScrolledText(result_window, wrap=tk.WORD)
                result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                result_text.insert(tk.END, summary)
                result_text.config(state=tk.DISABLED)

                # 按钮框架
                button_frame = ttk.Frame(result_window)
                button_frame.pack(pady=10)

                ttk.Button(button_frame, text="打开结果文件",
                          command=lambda: os.startfile(result_path)).pack(side=tk.LEFT, padx=5)
                ttk.Button(button_frame, text="运行可视化",
                          command=lambda: self._run_visualization(result_path)).pack(side=tk.LEFT, padx=5)
                ttk.Button(button_frame, text="关闭",
                          command=result_window.destroy).pack(side=tk.LEFT, padx=5)

        except Exception as e:
            self.log(f"✗ 显示结果失败: {str(e)}")

    def _run_visualization(self, result_path):
        """运行可视化"""
        try:
            cmd = [
                sys.executable,
                "visualize_nodules_optimized.py",
                "--predictions", result_path,
                "--debug"
            ]

            subprocess.Popen(cmd)
            self.log("已启动可视化程序")

        except Exception as e:
            self.log(f"✗ 启动可视化失败: {str(e)}")

def main():
    """主函数"""
    root = tk.Tk()

    # 设置主题样式
    style = ttk.Style()
    if 'vista' in style.theme_names():
        style.theme_use('vista')
    elif 'clam' in style.theme_names():
        style.theme_use('clam')

    app = DetectionConfigGUI(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass

    root.mainloop()

if __name__ == "__main__":
    main()
