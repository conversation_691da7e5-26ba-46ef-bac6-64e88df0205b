# AI-in-Lung-Detection 项目中文使用说明

## 1. 项目简介

本项目是一个基于人工智能的肺结节分析工具集，主要包含两大功能模块：

1.  **肺结节智能诊断**：利用深度学习模型，自动完成对CT影像中肺结节的**检测**、**分割**和**良恶性分类**，并生成图文并茂的可视化报告。
2.  **影像组学特征提取**：从分割出的结节中，提取上百种影像组学特征，为后续的科学研究和统计分析提供量化数据。

由于两个功能模块依赖的底层计算库不同，本项目需要在**两个独立配置的Conda虚拟环境**中运行。本文档将详细指导您如何配置环境并使用项目的所有核心功能。

---

## 2. 冗余文件说明

在开始之前，请注意项目文件夹中包含一些非核心文件。这些文件对于运行主要功能不是必需的，您可以安全地忽略它们：

*   **IDE配置文件夹**: `.idea/`, `.cursor/`
*   **Python缓存**: `__pycache__/`
*   **旧文档**: `README/`, `readme_figures/`
*   **示例与开发脚本**: `radiomics_config_example.yaml`, `test.py`, `training.py`等。

本指南将聚焦于运行项目所需的核心文件。

---

## 3. 环境配置

您需要创建两个独立的Conda环境。我们分别称之为 `lung_detection`（用于智能诊断）和 `lung_radiomics`（用于影像组学分析）。

### 环境一：`lung_detection` (智能诊断环境)

这个环境用于运行结节的检测、分割和分类。

**步骤一：创建Conda环境**

打开您的终端（Anaconda Prompt），输入以下命令来创建一个新的Conda环境并激活它。

```bash
# 创建名为 lung_detection 的新环境，指定Python版本为3.8
conda create -n lung_detection python=3.8 -y

# 激活新创建的环境
conda activate lung_detection
```

**步骤二：安装依赖库**

此环境主要依赖 `PyTorch` 和 `MONAI`。请根据您的计算机是否拥有NVIDIA显卡（即是否有CUDA支持）选择合适的命令进行安装。

*   **如果您的电脑有NVIDIA显卡 (推荐)**:
    ```bash
    # 安装NVIDIA官方推荐的PyTorch版本（请访问PyTorch官网获取最新命令）
    # 以下命令以CUDA 11.7为例
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu117
    
    # 安装其他依赖
    pip install monai nibabel pandas numpy matplotlib scikit-image
    ```

*   **如果您的电脑没有NVIDIA显卡 (仅使用CPU)**:
    ```bash
    # 安装CPU版本的PyTorch
    pip install torch torchvision torchaudio
    
    # 安装其他依赖
    pip install monai nibabel pandas numpy matplotlib scikit-image
    ```

### 环境二：`lung_radiomics` (影像组学环境)

这个环境专门用于提取影像组学特征。

**步骤一：创建并激活环境**

```bash
# 创建名为 lung_radiomics 的新环境
conda create -n lung_radiomics python=3.8 -y

# 激活环境
conda activate lung_radiomics
```

**步骤二：安装依赖库**

此环境的核心是 `pyradiomics` 库。项目根目录下的 `radiomics_requirements.txt` 文件已经列出了所有需要的依赖。

```bash
# 使用pip和-r参数来安装所有指定的依赖
pip install -r radiomics_requirements.txt
```

---

## 4. 使用指南：肺结节智能诊断流程

请确保您当前处于 `lung_detection` 环境中 (`conda activate lung_detection`)。

### 第一步：整理原始CT数据 (`separate.py`)

当您从医院或公开数据集中获得CT影像时，通常是一个文件夹包含了上百个混杂在一起的 `.dcm` 文件。`separate.py` 工具可以帮助您自动将它们按病人/扫描序列分门别类。

1.  **运行脚本**: 在终端中执行以下命令，会弹出一个图形界面。
    ```bash
    python separate.py
    ```
2.  **选择输入文件夹**: 点击 "Select Input DICOM Folder"，选择存放着原始 `.dcm` 文件的文件夹。
3.  **选择输出文件夹**: 点击 "Select Output Base Folder"，选择一个空文件夹，整理好的数据将存放在这里。
4.  **开始整理**: 点击 "Separate DICOM Series"。程序会自动创建子文件夹，并将属于同一个扫描序列的 `.dcm` 文件归类到一起。

### 第二步：创建待处理数据列表 (手动操作)

您需要告诉程序您想要分析哪些CT影像。这需要手动创建一个名为 `datalist.json` 的文件。

1.  **找到整理好的数据**: 进入上一步的输出文件夹，您会看到一些由长串字母和数字命名的子文件夹。每一个子文件夹代表一个完整的CT扫描。
2.  **准备文件**:
    *   在项目根目录下的 `ct_detection` 文件夹中，创建一个名为 `datalist.json` 的新文本文件。
    *   **重要**：将`ct_detection`文件夹中的`test_data.json`文件内容作为模板复制到`datalist.json`中。
3.  **编辑 `datalist.json`**:
    *   打开 `datalist.json` 文件。
    *   将其中的 `"image": "path/to/your/ct/image.nii.gz"` 的路径修改为您在**第一步**中生成的其中一个CT扫描文件夹的**绝对路径**。请确保路径分隔符使用正斜杠 `/`。
    *   `"box"` 和 `"label"` 字段可以保留为空数组 `[]`，因为这是程序要预测的内容。

**示例 `datalist.json` 内容:**
```json
{
  "validation": [
    {
      "image": "D:/data/AI-in-Lung-Detection/output_separated/0a1b2c3d...",
      "box": [],
      "label": []
    }
  ]
}
```
> **提示**: 您也可以在 `"validation"` 列表中添加多个对象，以一次性处理多个CT扫描。

### 第三步：执行检测与分割 (`testing.py` 和 `nodule_segmentation_3d_fixed.py`)

这一步将自动完成结节的检测和精确分割。

1.  **运行检测脚本**:
    ```bash
    # 注意，我们需要进入ct_detection目录来运行它
    cd ct_detection
    python testing.py
    cd .. 
    ```
    运行结束后，会在 `ct_detection/output` 文件夹中生成 `results.json` 文件，其中包含了检测到的结节的边界框。

2.  **运行分割脚本**:
    ```bash
    python nodule_segmentation_3d_fixed.py
    ```
    此脚本会自动读取 `results.json` 的结果，并对每个结节进行3D分割，生成精确的掩码文件（mask），保存在 `output/segs` 目录中。

### 第四步：执行分类 (`detect_and_classify.py`)

这一步将对分割出的结节进行良恶性分类。

```bash
python detect_and_classify.py
```

程序会读取检测和分割的结果，并使用分类模型进行预测。最终结果保存在 `output/classification_results.json` 文件中。

### 第五步：可视化结果 (`visualize_classification.py`)

最后，将所有结果生成直观的可视化图片。

```bash
python visualize_classification.py
```

脚本会在 `output/vis` 文件夹中生成一系列图片。每张图片都会展示结节在CT影像中的三维位置（轴位、冠状位、矢状位），并用红框标出，同时在标题中注明其**良恶性分类结果和置信度**。

---

## 5. 使用指南：影像组学特征提取流程

请切换到 `lung_radiomics` 环境 (`conda activate lung_radiomics`)。

此流程依赖于**智能诊断流程**中生成的**分割掩码**。请确保您已经完成了第4节中的所有步骤。

### 执行特征提取 (`radiomics_feature_extractor.py`)

1.  **准备配置文件**: `radiomics_config_lung_nodule.yaml` 是影像组学特征提取的配置文件。您可以打开它，查看或修改需要提取的特征类型。通常默认配置即可。
2.  **运行脚本**:
    ```bash
    python radiomics_feature_extractor.py
    ```
    脚本会自动查找 `output/segs` 目录中的分割掩码文件，匹配原始CT影像，然后开始提取特征。

### 查看结果

运行结束后，会在 `output/radiomics` 文件夹中生成两个文件：

*   `radiomics_features.csv`: 一个表格文件，可以用Excel或WPS打开。每一行代表一个结节，每一列代表一种影像组学特征的值。
*   `radiomics_features.json`: 内容与CSV文件相同，格式为JSON。

这些数据可用于进一步的统计学分析和科学研究。

---

## 6. 总结

本项目通过两个独立的Conda环境，提供了一套从原始CT影像到智能诊断报告，再到科研数据提取的完整解决方案。希望这份详细的指南能帮助您顺利地使用本工具。如有问题，请检查每一步的输出是否与文档描述一致。 